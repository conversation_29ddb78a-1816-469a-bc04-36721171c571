import { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Star, ThumbsUp, CheckCircle, Flag, MoreHorizontal } from 'lucide-react';
import { Review } from '@/data';

interface ReviewCardProps {
  review: Review;
  onHelpful?: (reviewId: string) => void;
  onReport?: (reviewId: string) => void;
}

export function ReviewCard({ review, onHelpful, onReport }: ReviewCardProps) {
  const [isHelpful, setIsHelpful] = useState(false);
  const [showFullReview, setShowFullReview] = useState(false);

  const renderStars = (rating: number) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-4 w-4 ${
              star <= rating 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'text-gray-300'
            }`}
          />
        ))}
      </div>
    );
  };

  const handleHelpful = () => {
    setIsHelpful(!isHelpful);
    if (onHelpful) {
      onHelpful(review.id);
    }
  };

  const isLongReview = review.comment.length > 300;
  const displayComment = showFullReview || !isLongReview 
    ? review.comment 
    : review.comment.substring(0, 300) + '...';

  return (
    <div className="border-b border-border pb-6 last:border-b-0">
      <div className="flex items-start gap-4">
        <Avatar className="h-10 w-10">
          <AvatarImage src={review.userAvatar} />
          <AvatarFallback>{review.userName.charAt(0)}</AvatarFallback>
        </Avatar>
        
        <div className="flex-1 min-w-0">
          {/* Header */}
          <div className="flex items-center gap-2 mb-2">
            <span className="font-semibold">{review.userName}</span>
            {review.verified && (
              <Badge variant="secondary" className="text-xs">
                <CheckCircle className="h-3 w-3 mr-1" />
                Verified
              </Badge>
            )}
            <span className="text-sm text-muted-foreground">
              {new Date(review.date).toLocaleDateString('en-ZA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </span>
          </div>
          
          {/* Rating */}
          <div className="flex items-center gap-2 mb-3">
            {renderStars(review.rating)}
            <span className="text-sm font-medium">{review.rating}/5</span>
          </div>
          
          {/* Review Title */}
          {review.title && (
            <h4 className="font-medium mb-2 text-foreground">{review.title}</h4>
          )}
          
          {/* Review Content */}
          <div className="mb-3">
            <p className="text-muted-foreground leading-relaxed">
              {displayComment}
            </p>
            {isLongReview && (
              <Button
                variant="link"
                className="p-0 h-auto text-sm"
                onClick={() => setShowFullReview(!showFullReview)}
              >
                {showFullReview ? 'Show less' : 'Read more'}
              </Button>
            )}
          </div>
          
          {/* Review Images */}
          {review.images && review.images.length > 0 && (
            <div className="flex gap-2 mb-4 overflow-x-auto">
              {review.images.map((image, index) => (
                <div key={index} className="flex-shrink-0 w-20 h-20 rounded-lg overflow-hidden">
                  <img 
                    src={image} 
                    alt={`Review image ${index + 1}`}
                    className="w-full h-full object-cover hover:scale-105 transition-transform cursor-pointer"
                  />
                </div>
              ))}
            </div>
          )}
          
          {/* Actions */}
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              className={`text-sm ${isHelpful ? 'text-primary' : 'text-muted-foreground'}`}
              onClick={handleHelpful}
            >
              <ThumbsUp className={`h-3 w-3 mr-1 ${isHelpful ? 'fill-current' : ''}`} />
              Helpful ({review.helpful + (isHelpful ? 1 : 0)})
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="text-sm text-muted-foreground"
              onClick={() => onReport && onReport(review.id)}
            >
              <Flag className="h-3 w-3 mr-1" />
              Report
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              className="text-sm text-muted-foreground"
            >
              <MoreHorizontal className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

// Component for displaying a list of reviews with filtering and sorting
interface ReviewListProps {
  reviews: Review[];
  showSummary?: boolean;
  maxReviews?: number;
}

export function ReviewList({ reviews, showSummary = true, maxReviews }: ReviewListProps) {
  const [sortBy, setSortBy] = useState<'newest' | 'oldest' | 'highest' | 'lowest'>('newest');
  const [filterRating, setFilterRating] = useState<number | null>(null);

  // Sort reviews
  const sortedReviews = [...reviews].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case 'oldest':
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      case 'highest':
        return b.rating - a.rating;
      case 'lowest':
        return a.rating - b.rating;
      default:
        return 0;
    }
  });

  // Filter by rating
  const filteredReviews = filterRating 
    ? sortedReviews.filter(review => review.rating === filterRating)
    : sortedReviews;

  // Limit number of reviews if specified
  const displayReviews = maxReviews 
    ? filteredReviews.slice(0, maxReviews)
    : filteredReviews;

  if (reviews.length === 0) {
    return (
      <div className="text-center py-8">
        <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-semibold mb-2">No reviews yet</h3>
        <p className="text-muted-foreground">
          Be the first to share your experience at this campsite!
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filters and Sorting */}
      <div className="flex flex-wrap items-center gap-4 pb-4 border-b">
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Sort by:</span>
          <select 
            value={sortBy} 
            onChange={(e) => setSortBy(e.target.value as any)}
            className="text-sm border rounded px-2 py-1"
          >
            <option value="newest">Newest first</option>
            <option value="oldest">Oldest first</option>
            <option value="highest">Highest rated</option>
            <option value="lowest">Lowest rated</option>
          </select>
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Filter:</span>
          <div className="flex gap-1">
            <Button
              variant={filterRating === null ? "default" : "outline"}
              size="sm"
              onClick={() => setFilterRating(null)}
            >
              All
            </Button>
            {[5, 4, 3, 2, 1].map(rating => (
              <Button
                key={rating}
                variant={filterRating === rating ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterRating(rating)}
              >
                {rating}★
              </Button>
            ))}
          </div>
        </div>
      </div>

      {/* Reviews */}
      <div className="space-y-6">
        {displayReviews.map(review => (
          <ReviewCard key={review.id} review={review} />
        ))}
      </div>

      {/* Show More Button */}
      {maxReviews && filteredReviews.length > maxReviews && (
        <div className="text-center pt-4">
          <Button variant="outline">
            Show all {filteredReviews.length} reviews
          </Button>
        </div>
      )}
    </div>
  );
}
