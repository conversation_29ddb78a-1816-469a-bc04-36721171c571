import { Button } from '@/components/ui/button';
import { Heart } from 'lucide-react';
import { useWishlistButton } from '@/contexts/WishlistContext';
import { cn } from '@/lib/utils';

interface WishlistButtonProps {
  campsiteId: string;
  variant?: 'default' | 'secondary' | 'ghost' | 'outline';
  size?: 'sm' | 'default' | 'lg';
  className?: string;
  showText?: boolean;
}

export function WishlistButton({ 
  campsiteId, 
  variant = 'secondary', 
  size = 'sm',
  className,
  showText = false
}: WishlistButtonProps) {
  const { isFavorite, toggleFavorite } = useWishlistButton(campsiteId);

  return (
    <Button
      variant={variant}
      size={size}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        toggleFavorite();
      }}
      className={cn(className)}
      title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}
    >
      <Heart 
        className={cn(
          'h-4 w-4',
          showText && 'mr-2',
          isFavorite && 'fill-red-500 text-red-500'
        )} 
      />
      {showText && (isFavorite ? 'Saved' : 'Save')}
    </Button>
  );
}
