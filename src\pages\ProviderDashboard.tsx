import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown,
  DollarSign, 
  Calendar, 
  Users, 
  Star,
  MapPin,
  Settings,
  Plus,
  Eye,
  MessageSquare,
  Edit,
  MoreHorizontal
} from 'lucide-react';
import { mockProviders, mockAnalytics, allCampsites } from '@/data';

const ProviderDashboard = () => {
  // Mock current provider - in a real app this would come from auth context
  const currentProvider = mockProviders[0]; // Mountain View Camping
  const [selectedPeriod, setSelectedPeriod] = useState('30d');

  // Get provider's campsites
  const providerCampsites = allCampsites.filter(site => site.providerId === currentProvider.id);
  
  // Mock analytics data for charts
  const revenueData = [
    { month: 'Jan', revenue: 12500, bookings: 45 },
    { month: 'Feb', revenue: 15200, bookings: 52 },
    { month: 'Mar', revenue: 18900, bookings: 67 },
    { month: 'Apr', revenue: 22100, bookings: 78 },
    { month: 'May', revenue: 19800, bookings: 71 },
    { month: 'Jun', revenue: 25600, bookings: 89 },
    { month: 'Jul', revenue: 31200, bookings: 112 },
    { month: 'Aug', revenue: 28900, bookings: 98 },
    { month: 'Sep', revenue: 24700, bookings: 85 },
    { month: 'Oct', revenue: 27300, bookings: 94 },
    { month: 'Nov', revenue: 23800, bookings: 82 },
    { month: 'Dec', revenue: 29500, bookings: 105 }
  ];

  const occupancyData = [
    { name: 'Jan', occupancy: 65 },
    { name: 'Feb', occupancy: 72 },
    { name: 'Mar', occupancy: 78 },
    { name: 'Apr', occupancy: 85 },
    { name: 'May', occupancy: 82 },
    { name: 'Jun', occupancy: 91 },
    { name: 'Jul', occupancy: 95 },
    { name: 'Aug', occupancy: 93 },
    { name: 'Sep', occupancy: 87 },
    { name: 'Oct', occupancy: 89 },
    { name: 'Nov', occupancy: 84 },
    { name: 'Dec', occupancy: 92 }
  ];

  const bookingSourceData = [
    { name: 'Direct Bookings', value: 45, color: '#8884d8' },
    { name: 'Search Results', value: 35, color: '#82ca9d' },
    { name: 'Featured Listings', value: 15, color: '#ffc658' },
    { name: 'Referrals', value: 5, color: '#ff7c7c' }
  ];

  const currentMonthStats = {
    revenue: 29500,
    bookings: 105,
    occupancy: 92,
    avgRating: 4.7,
    revenueChange: 12.5,
    bookingsChange: 8.2,
    occupancyChange: 5.1,
    ratingChange: 0.2
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Provider Dashboard</h1>
            <p className="text-muted-foreground">Welcome back, {currentProvider.name}</p>
          </div>
          
          <div className="flex items-center gap-3">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Campsite
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Monthly Revenue</p>
                  <p className="text-2xl font-bold">R{currentMonthStats.revenue.toLocaleString()}</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-500 font-medium">
                  +{currentMonthStats.revenueChange}%
                </span>
                <span className="text-sm text-muted-foreground ml-2">vs last month</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Total Bookings</p>
                  <p className="text-2xl font-bold">{currentMonthStats.bookings}</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Calendar className="h-6 w-6 text-blue-600" />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-500 font-medium">
                  +{currentMonthStats.bookingsChange}%
                </span>
                <span className="text-sm text-muted-foreground ml-2">vs last month</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Occupancy Rate</p>
                  <p className="text-2xl font-bold">{currentMonthStats.occupancy}%</p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-500 font-medium">
                  +{currentMonthStats.occupancyChange}%
                </span>
                <span className="text-sm text-muted-foreground ml-2">vs last month</span>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Average Rating</p>
                  <p className="text-2xl font-bold">{currentMonthStats.avgRating}</p>
                </div>
                <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <Star className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
              <div className="flex items-center mt-4">
                <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
                <span className="text-sm text-green-500 font-medium">
                  +{currentMonthStats.ratingChange}
                </span>
                <span className="text-sm text-muted-foreground ml-2">vs last month</span>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Charts and Analytics */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Revenue Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Revenue Trends</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={revenueData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`R${value}`, 'Revenue']} />
                  <Bar dataKey="revenue" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Occupancy Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Occupancy Rate</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={occupancyData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`${value}%`, 'Occupancy']} />
                  <Line type="monotone" dataKey="occupancy" stroke="#82ca9d" strokeWidth={2} />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </div>

        {/* Booking Sources and Campsite Management */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Booking Sources */}
          <Card>
            <CardHeader>
              <CardTitle>Booking Sources</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={bookingSourceData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {bookingSourceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>

          {/* Commission Tracking */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Commission & Earnings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">R26,550</div>
                  <div className="text-sm text-muted-foreground">Net Earnings</div>
                  <div className="text-xs text-muted-foreground mt-1">After 3% commission</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-red-600">R885</div>
                  <div className="text-sm text-muted-foreground">Commission Paid</div>
                  <div className="text-xs text-muted-foreground mt-1">3% of R29,500</div>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">R29,500</div>
                  <div className="text-sm text-muted-foreground">Gross Revenue</div>
                  <div className="text-xs text-muted-foreground mt-1">This month</div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-semibold">Recent Transactions</h4>
                <div className="space-y-2">
                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded">
                    <div>
                      <div className="font-medium">Booking #BK-2024-001</div>
                      <div className="text-sm text-muted-foreground">Mountain View Campsite • 3 nights</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">R2,850</div>
                      <div className="text-sm text-red-600">-R85.50 commission</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded">
                    <div>
                      <div className="font-medium">Booking #BK-2024-002</div>
                      <div className="text-sm text-muted-foreground">Riverside Retreat • 2 nights</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">R1,800</div>
                      <div className="text-sm text-red-600">-R54.00 commission</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-muted/30 rounded">
                    <div>
                      <div className="font-medium">Payout #PO-2024-12</div>
                      <div className="text-sm text-muted-foreground">Weekly payout to bank</div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium text-green-600">+R8,750</div>
                      <div className="text-sm text-muted-foreground">Processed</div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Campsite Management */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Your Campsites</CardTitle>
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add New
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {providerCampsites.map(campsite => (
                <div key={campsite.id} className="flex items-center gap-4 p-4 border rounded-lg">
                  <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                    <img 
                      src={campsite.image} 
                      alt={campsite.name}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold">{campsite.name}</h3>
                      {campsite.featured && (
                        <Badge variant="secondary">Featured</Badge>
                      )}
                    </div>
                    
                    <div className="flex items-center text-muted-foreground text-sm mb-2">
                      <MapPin className="h-3 w-3 mr-1" />
                      {campsite.location}, {campsite.province}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm">
                      <div className="flex items-center">
                        <Star className="h-3 w-3 text-yellow-500 mr-1" />
                        {campsite.rating} ({campsite.reviewCount} reviews)
                      </div>
                      <div className="flex items-center">
                        <Eye className="h-3 w-3 mr-1" />
                        1.2k views
                      </div>
                      <div className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        85% occupancy
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="font-semibold text-lg">R{campsite.price}/night</div>
                    <div className="text-sm text-muted-foreground">R12,500/month</div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Button variant="outline" size="sm">
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button variant="outline" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ProviderDashboard;
