import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Calendar as CalendarIcon,
  Percent,
  Star,
  TrendingUp,
  Eye,
  Users,
  DollarSign
} from 'lucide-react';
import { format } from 'date-fns';
import { mockPromotions, allCampsites } from '@/data';

const PromotionsManager = () => {
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [selectedPromotion, setSelectedPromotion] = useState<any>(null);
  const [startDate, setStartDate] = useState<Date>();
  const [endDate, setEndDate] = useState<Date>();

  const [newPromotion, setNewPromotion] = useState({
    title: '',
    description: '',
    discountType: 'percentage',
    discountValue: 0,
    campsiteId: '',
    startDate: '',
    endDate: '',
    maxUses: 0,
    minNights: 1,
    isActive: true,
    code: ''
  });

  // Mock provider campsites
  const providerCampsites = allCampsites.slice(0, 3);

  const handleCreatePromotion = () => {
    console.log('Creating promotion:', newPromotion);
    setShowCreateDialog(false);
    setNewPromotion({
      title: '',
      description: '',
      discountType: 'percentage',
      discountValue: 0,
      campsiteId: '',
      startDate: '',
      endDate: '',
      maxUses: 0,
      minNights: 1,
      isActive: true,
      code: ''
    });
  };

  const getPromotionStatus = (promotion: any) => {
    const now = new Date();
    const start = new Date(promotion.startDate);
    const end = new Date(promotion.endDate);
    
    if (now < start) return 'upcoming';
    if (now > end) return 'expired';
    if (!promotion.isActive) return 'paused';
    return 'active';
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'upcoming': return 'bg-blue-100 text-blue-800';
      case 'expired': return 'bg-gray-100 text-gray-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold">Promotions & Pricing</h1>
            <p className="text-muted-foreground">Manage your special offers and dynamic pricing</p>
          </div>
          
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Promotion
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Promotion</DialogTitle>
                <DialogDescription>
                  Set up a special offer to attract more bookings
                </DialogDescription>
              </DialogHeader>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="title">Promotion Title</Label>
                    <Input
                      id="title"
                      placeholder="e.g., Summer Special"
                      value={newPromotion.title}
                      onChange={(e) => setNewPromotion(prev => ({ ...prev, title: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="code">Promo Code (Optional)</Label>
                    <Input
                      id="code"
                      placeholder="e.g., SUMMER20"
                      value={newPromotion.code}
                      onChange={(e) => setNewPromotion(prev => ({ ...prev, code: e.target.value }))}
                    />
                  </div>
                </div>
                
                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    placeholder="Describe your promotion..."
                    value={newPromotion.description}
                    onChange={(e) => setNewPromotion(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Discount Type</Label>
                    <Select 
                      value={newPromotion.discountType} 
                      onValueChange={(value) => setNewPromotion(prev => ({ ...prev, discountType: value }))}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="percentage">Percentage Off</SelectItem>
                        <SelectItem value="fixed">Fixed Amount Off</SelectItem>
                        <SelectItem value="free_nights">Free Nights</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="discountValue">Discount Value</Label>
                    <Input
                      id="discountValue"
                      type="number"
                      placeholder={newPromotion.discountType === 'percentage' ? '20' : '500'}
                      value={newPromotion.discountValue}
                      onChange={(e) => setNewPromotion(prev => ({ ...prev, discountValue: Number(e.target.value) }))}
                    />
                  </div>
                </div>
                
                <div>
                  <Label>Apply to Campsite</Label>
                  <Select 
                    value={newPromotion.campsiteId} 
                    onValueChange={(value) => setNewPromotion(prev => ({ ...prev, campsiteId: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select campsite" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Campsites</SelectItem>
                      {providerCampsites.map(campsite => (
                        <SelectItem key={campsite.id} value={campsite.id}>
                          {campsite.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Start Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {startDate ? format(startDate, "PPP") : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={startDate}
                          onSelect={setStartDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div>
                    <Label>End Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {endDate ? format(endDate, "PPP") : "Pick a date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0">
                        <Calendar
                          mode="single"
                          selected={endDate}
                          onSelect={setEndDate}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="maxUses">Max Uses (0 = unlimited)</Label>
                    <Input
                      id="maxUses"
                      type="number"
                      value={newPromotion.maxUses}
                      onChange={(e) => setNewPromotion(prev => ({ ...prev, maxUses: Number(e.target.value) }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="minNights">Minimum Nights</Label>
                    <Input
                      id="minNights"
                      type="number"
                      value={newPromotion.minNights}
                      onChange={(e) => setNewPromotion(prev => ({ ...prev, minNights: Number(e.target.value) }))}
                    />
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={newPromotion.isActive}
                    onCheckedChange={(checked) => setNewPromotion(prev => ({ ...prev, isActive: checked }))}
                  />
                  <Label htmlFor="isActive">Active immediately</Label>
                </div>
                
                <div className="flex justify-end gap-2">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreatePromotion}>
                    Create Promotion
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Promotion Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Active Promotions</p>
                  <p className="text-2xl font-bold">3</p>
                </div>
                <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Percent className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Promotion Revenue</p>
                  <p className="text-2xl font-bold">R15,200</p>
                </div>
                <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <DollarSign className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Bookings from Promos</p>
                  <p className="text-2xl font-bold">47</p>
                </div>
                <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                  <Users className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">Avg. Discount</p>
                  <p className="text-2xl font-bold">18%</p>
                </div>
                <div className="h-12 w-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Promotions List */}
        <Card>
          <CardHeader>
            <CardTitle>Your Promotions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockPromotions.map(promotion => {
                const status = getPromotionStatus(promotion);
                const campsite = allCampsites.find(c => c.id === promotion.campsiteId);
                
                return (
                  <div key={promotion.id} className="flex items-center gap-4 p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold text-lg">{promotion.title}</h3>
                        <Badge className={getStatusColor(status)}>
                          {status}
                        </Badge>
                        {promotion.featured && (
                          <Badge variant="secondary">
                            <Star className="h-3 w-3 mr-1" />
                            Featured
                          </Badge>
                        )}
                      </div>
                      
                      <p className="text-muted-foreground mb-3">{promotion.description}</p>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <span className="text-muted-foreground">Discount:</span>
                          <div className="font-medium">
                            {promotion.discountType === 'percentage' 
                              ? `${promotion.discountValue}% off`
                              : `R${promotion.discountValue} off`
                            }
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Campsite:</span>
                          <div className="font-medium">{campsite?.name || 'All Sites'}</div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Valid:</span>
                          <div className="font-medium">
                            {new Date(promotion.startDate).toLocaleDateString()} - {new Date(promotion.endDate).toLocaleDateString()}
                          </div>
                        </div>
                        <div>
                          <span className="text-muted-foreground">Uses:</span>
                          <div className="font-medium">
                            {promotion.usedCount}/{promotion.maxUses || '∞'}
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PromotionsManager;
