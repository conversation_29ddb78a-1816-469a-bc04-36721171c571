import { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BookOpen, 
  MessageSquare, 
  TrendingUp, 
  Clock, 
  Eye, 
  ThumbsUp,
  Search,
  Filter,
  Plus,
  CheckCircle,
  Users
} from 'lucide-react';
import { mockBlogPosts, mockForumPosts } from '@/data';

const Community = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [sortBy, setSortBy] = useState('newest');
  const navigate = useNavigate();

  // Check if data is available
  if (!mockBlogPosts || mockBlogPosts.length === 0 || !mockForumPosts || mockForumPosts.length === 0) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Loading community content...</h1>
            <p>Please wait while we load the blog posts and forum discussions.</p>
          </div>
        </div>
      </div>
    );
  }

  const handleCreatePost = () => {
    // For now, navigate to profile page - in a real app this would open a create post modal
    navigate('/profile');
  };

  // Filter blog posts
  const filteredBlogPosts = mockBlogPosts.filter(post => {
    const matchesSearch = searchQuery === '' || 
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesCategory = selectedCategory === '' || post.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Filter forum posts
  const filteredForumPosts = mockForumPosts.filter(post => {
    const matchesSearch = searchQuery === '' || 
      post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      post.content.toLowerCase().includes(searchQuery.toLowerCase());
    
    const matchesCategory = selectedCategory === '' || post.category === selectedCategory;
    
    return matchesSearch && matchesCategory;
  });

  // Sort posts
  const sortedBlogPosts = [...filteredBlogPosts].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.publishDate).getTime() - new Date(a.publishDate).getTime();
      case 'oldest':
        return new Date(a.publishDate).getTime() - new Date(b.publishDate).getTime();
      case 'popular':
        return b.readTime - a.readTime; // Mock popularity by read time
      default:
        return 0;
    }
  });

  const sortedForumPosts = [...filteredForumPosts].sort((a, b) => {
    switch (sortBy) {
      case 'newest':
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      case 'oldest':
        return new Date(a.date).getTime() - new Date(b.date).getTime();
      case 'popular':
        return b.likes - a.likes;
      default:
        return 0;
    }
  });

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'tips': return '💡';
      case 'destinations': return '🏔️';
      case 'gear': return '🎒';
      case 'guides': return '📖';
      case 'general': return '💬';
      case 'help': return '❓';
      default: return '📝';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'tips': return 'bg-blue-100 text-blue-800';
      case 'destinations': return 'bg-green-100 text-green-800';
      case 'gear': return 'bg-purple-100 text-purple-800';
      case 'guides': return 'bg-orange-100 text-orange-800';
      case 'general': return 'bg-gray-100 text-gray-800';
      case 'help': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Debug info */}
      <div className="bg-blue-100 border-2 border-blue-500 p-4 m-4 text-black">
        <h2 className="text-xl font-bold text-blue-800">👥 COMMUNITY PAGE DEBUG INFO</h2>
        <p className="text-lg">Blog posts: {mockBlogPosts?.length || 0}</p>
        <p className="text-lg">Forum posts: {mockForumPosts?.length || 0}</p>
        <p className="text-lg">Filtered blog posts: {filteredBlogPosts?.length || 0}</p>
        <p className="text-lg">Filtered forum posts: {filteredForumPosts?.length || 0}</p>
        <p className="text-lg">Page is rendering: ✅</p>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">CampSpot Community</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Connect with fellow campers, share experiences, and discover the best of South African camping
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search posts, guides, and discussions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="All categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All categories</SelectItem>
              <SelectItem value="tips">Tips & Advice</SelectItem>
              <SelectItem value="destinations">Destinations</SelectItem>
              <SelectItem value="gear">Gear & Equipment</SelectItem>
              <SelectItem value="guides">Guides</SelectItem>
              <SelectItem value="general">General Discussion</SelectItem>
              <SelectItem value="help">Help & Support</SelectItem>
            </SelectContent>
          </Select>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="oldest">Oldest</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Community Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <BookOpen className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{mockBlogPosts.length}</div>
              <div className="text-sm text-muted-foreground">Blog Posts</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <MessageSquare className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">{mockForumPosts.length}</div>
              <div className="text-sm text-muted-foreground">Discussions</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <Users className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">1.2k</div>
              <div className="text-sm text-muted-foreground">Active Members</div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-4 text-center">
              <TrendingUp className="h-8 w-8 text-primary mx-auto mb-2" />
              <div className="text-2xl font-bold">89%</div>
              <div className="text-sm text-muted-foreground">Helpful Answers</div>
            </CardContent>
          </Card>
        </div>

        {/* Content Tabs */}
        <Tabs defaultValue="blog" className="space-y-6">
          <div className="flex items-center justify-between">
            <TabsList>
              <TabsTrigger value="blog">
                <BookOpen className="h-4 w-4 mr-2" />
                Blog & Guides
              </TabsTrigger>
              <TabsTrigger value="forum">
                <MessageSquare className="h-4 w-4 mr-2" />
                Community Forum
              </TabsTrigger>
            </TabsList>

            <Button onClick={handleCreatePost}>
              <Plus className="h-4 w-4 mr-2" />
              Create Post
            </Button>
          </div>

          {/* Blog Tab */}
          <TabsContent value="blog" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {sortedBlogPosts.map(post => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                  <Link to={`/blog/${post.slug}`} className="block">
                  <div className="relative h-48">
                    <img 
                      src={post.image} 
                      alt={post.title}
                      className="w-full h-full object-cover"
                    />
                    <Badge className={`absolute top-4 left-4 ${getCategoryColor(post.category)}`}>
                      {getCategoryIcon(post.category)} {post.category}
                    </Badge>
                  </div>
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-lg mb-2 line-clamp-2">{post.title}</h3>
                    <p className="text-muted-foreground text-sm mb-3 line-clamp-3">{post.excerpt}</p>
                    
                    <div className="flex items-center gap-3 mb-3">
                      <Avatar className="h-6 w-6">
                        <AvatarImage src={post.authorAvatar} />
                        <AvatarFallback className="text-xs">{post.author.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <span className="text-sm text-muted-foreground">{post.author}</span>
                    </div>
                    
                    <div className="flex items-center justify-between text-sm text-muted-foreground">
                      <div className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {post.readTime} min read
                      </div>
                      <span>{new Date(post.publishDate).toLocaleDateString()}</span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1 mt-3">
                      {post.tags.slice(0, 3).map(tag => (
                        <Badge key={tag} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                  </Link>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Forum Tab */}
          <TabsContent value="forum" className="space-y-6">
            <div className="space-y-4">
              {sortedForumPosts.map(post => (
                <Card key={post.id} className="hover:shadow-md transition-shadow cursor-pointer" onClick={() => navigate('/community')}>
                  {/* Forum posts would typically navigate to individual discussion pages */}
                  <CardContent className="p-6">
                    <div className="flex items-start gap-4">
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={post.userAvatar} />
                        <AvatarFallback>{post.userName.charAt(0)}</AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-lg">{post.title}</h3>
                          {post.solved && (
                            <Badge variant="secondary" className="text-xs">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Solved
                            </Badge>
                          )}
                          <Badge className={`text-xs ${getCategoryColor(post.category)}`}>
                            {getCategoryIcon(post.category)} {post.category}
                          </Badge>
                        </div>
                        
                        <p className="text-muted-foreground mb-3 line-clamp-2">{post.content}</p>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="font-medium text-foreground">{post.userName}</span>
                          <span>{new Date(post.date).toLocaleDateString()}</span>
                          <div className="flex items-center">
                            <MessageSquare className="h-3 w-3 mr-1" />
                            {post.replies || 0} replies
                          </div>
                          <div className="flex items-center">
                            <Eye className="h-3 w-3 mr-1" />
                            {post.views} views
                          </div>
                          <div className="flex items-center">
                            <ThumbsUp className="h-3 w-3 mr-1" />
                            {post.likes} likes
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Community;
