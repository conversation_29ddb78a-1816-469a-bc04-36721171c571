import { useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Navigation } from '@/components/ui/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import BookingCalendar from '@/components/BookingCalendar';
import PaymentOptions from '@/components/PaymentOptions';
import { getCampsiteById } from '@/data';
import { Star, MapPin, Users, Calendar, Clock, CheckCircle } from 'lucide-react';

const Booking = () => {
  const { id } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const [currentStep, setCurrentStep] = useState(1);
  const [bookingData, setBookingData] = useState({
    checkIn: null as Date | null,
    checkOut: null as Date | null,
    guests: parseInt(searchParams.get('guests') || '2'),
    totalPrice: 0
  });

  const campsite = getCampsiteById(id || '');

  if (!campsite) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Campsite not found</h1>
            <Button asChild>
              <a href="/search">Back to Search</a>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const handleDateSelect = (checkIn: Date, checkOut: Date) => {
    const nights = Math.ceil((checkOut.getTime() - checkIn.getTime()) / (1000 * 60 * 60 * 24));
    const totalPrice = nights * campsite.price;
    
    setBookingData(prev => ({
      ...prev,
      checkIn,
      checkOut,
      totalPrice
    }));
  };

  const handlePaymentSelect = (method: string) => {
    console.log('Payment method selected:', method);
  };

  const steps = [
    { number: 1, title: 'Select Dates', description: 'Choose your check-in and check-out dates' },
    { number: 2, title: 'Review & Pay', description: 'Review booking details and complete payment' },
    { number: 3, title: 'Confirmation', description: 'Booking confirmed!' }
  ];

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <BookingCalendar
              campsiteId={campsite.id}
              price={campsite.price}
              onDateSelect={handleDateSelect}
            />
            
            {bookingData.checkIn && bookingData.checkOut && (
              <div className="flex justify-end">
                <Button onClick={() => setCurrentStep(2)}>
                  Continue to Payment
                </Button>
              </div>
            )}
          </div>
        );
        
      case 2:
        return (
          <div className="space-y-6">
            {/* Booking Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Booking Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <img 
                    src={campsite.image} 
                    alt={campsite.name}
                    className="w-20 h-20 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="font-semibold">{campsite.name}</h3>
                    <div className="flex items-center text-muted-foreground text-sm">
                      <MapPin className="h-3 w-3 mr-1" />
                      {campsite.location}, {campsite.province}
                    </div>
                    <div className="flex items-center mt-1">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
                      <span className="text-sm">{campsite.rating} ({campsite.reviewCount} reviews)</span>
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="font-medium">Check-in</div>
                    <div className="text-muted-foreground">
                      {bookingData.checkIn?.toLocaleDateString('en-ZA', { 
                        weekday: 'short', 
                        year: 'numeric', 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Check-out</div>
                    <div className="text-muted-foreground">
                      {bookingData.checkOut?.toLocaleDateString('en-ZA', { 
                        weekday: 'short', 
                        year: 'numeric', 
                        month: 'short', 
                        day: 'numeric' 
                      })}
                    </div>
                  </div>
                  <div>
                    <div className="font-medium">Guests</div>
                    <div className="text-muted-foreground">{bookingData.guests} guests</div>
                  </div>
                  <div>
                    <div className="font-medium">Nights</div>
                    <div className="text-muted-foreground">
                      {bookingData.checkIn && bookingData.checkOut ? 
                        Math.ceil((bookingData.checkOut.getTime() - bookingData.checkIn.getTime()) / (1000 * 60 * 60 * 24)) : 0} nights
                    </div>
                  </div>
                </div>
                
                <Separator />
                
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span>R{bookingData.totalPrice.toLocaleString()}</span>
                </div>
              </CardContent>
            </Card>
            
            <PaymentOptions
              amount={bookingData.totalPrice}
              onPaymentSelect={handlePaymentSelect}
            />
            
            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentStep(1)}>
                Back to Dates
              </Button>
              <Button onClick={() => setCurrentStep(3)}>
                Complete Booking
              </Button>
            </div>
          </div>
        );
        
      case 3:
        return (
          <div className="text-center space-y-6">
            <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            
            <div>
              <h2 className="text-2xl font-bold text-green-600 mb-2">Booking Confirmed!</h2>
              <p className="text-muted-foreground">
                Your booking at {campsite.name} has been confirmed.
              </p>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="text-left space-y-2">
                  <h3 className="font-semibold">Booking Reference</h3>
                  <p className="text-2xl font-mono">CSA-{Date.now().toString().slice(-6)}</p>
                  
                  <div className="grid grid-cols-2 gap-4 mt-4 text-sm">
                    <div>
                      <div className="font-medium">Dates</div>
                      <div className="text-muted-foreground">
                        {bookingData.checkIn?.toLocaleDateString()} - {bookingData.checkOut?.toLocaleDateString()}
                      </div>
                    </div>
                    <div>
                      <div className="font-medium">Total Paid</div>
                      <div className="text-muted-foreground">R{bookingData.totalPrice.toLocaleString()}</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                A confirmation email has been sent to your email address with all the booking details.
              </p>
              
              <div className="flex gap-3 justify-center">
                <Button asChild>
                  <a href="/profile">View My Bookings</a>
                </Button>
                <Button variant="outline" asChild>
                  <a href="/search">Book Another Trip</a>
                </Button>
              </div>
            </div>
          </div>
        );
        
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Progress Steps */}
        <div className="mb-8">
          <div className="flex items-center justify-center mb-4">
            {steps.map((step, index) => (
              <div key={step.number} className="flex items-center">
                <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                  currentStep >= step.number 
                    ? 'bg-primary border-primary text-primary-foreground' 
                    : 'border-muted-foreground text-muted-foreground'
                }`}>
                  {currentStep > step.number ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    step.number
                  )}
                </div>
                
                {index < steps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-2 ${
                    currentStep > step.number ? 'bg-primary' : 'bg-muted'
                  }`} />
                )}
              </div>
            ))}
          </div>
          
          <div className="text-center">
            <h1 className="text-2xl font-bold">{steps[currentStep - 1].title}</h1>
            <p className="text-muted-foreground">{steps[currentStep - 1].description}</p>
          </div>
        </div>

        {/* Step Content */}
        <div className="max-w-2xl mx-auto">
          {renderStepContent()}
        </div>
      </div>
    </div>
  );
};

export default Booking;