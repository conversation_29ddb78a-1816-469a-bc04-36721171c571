import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MapPin, Search, Filter, Star, Users } from 'lucide-react';
import { allCampsites, SA_PROVINCES } from '@/data';

interface MapSearchProps {
  onFilterChange?: (filters: any) => void;
  selectedCampsites?: any[];
}

const MapSearch = ({ onFilterChange, selectedCampsites = [] }: MapSearchProps) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [selectedProvince, setSelectedProvince] = useState('all');
  const [priceRange, setPriceRange] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [hoveredCampsite, setHoveredCampsite] = useState<string | null>(null);

  // Mock coordinates for South African provinces
  const provinceCoordinates = {
    'Western Cape': { lat: -33.9, lng: 18.4 },
    'Eastern Cape': { lat: -32.3, lng: 26.4 },
    'Northern Cape': { lat: -29.1, lng: 21.8 },
    'Free State': { lat: -29.1, lng: 26.2 },
    'KwaZulu-Natal': { lat: -29.6, lng: 30.4 },
    'Gauteng': { lat: -26.2, lng: 28.0 },
    'Mpumalanga': { lat: -25.5, lng: 30.6 },
    'Limpopo': { lat: -23.9, lng: 29.4 },
    'North West': { lat: -26.2, lng: 25.6 }
  };

  const mockMapboxToken = 'pk.eyJ1IjoibG92YWJsZSIsImEiOiJjbHZud2lpMnAwNGF5MmpwZzJ3dHNhMGZhIn0.demo_token';

  useEffect(() => {
    // Simulate map loading
    const timer = setTimeout(() => {
      setMapLoaded(true);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const filteredCampsites = allCampsites.filter(campsite => {
    if (selectedProvince && selectedProvince !== 'all' && campsite.province !== selectedProvince) return false;
    if (priceRange !== 'all') {
      const [min, max] = priceRange.split('-').map(Number);
      if (campsite.price < min || campsite.price > max) return false;
    }
    if (selectedType !== 'all' && campsite.type !== selectedType) return false;
    return true;
  });

  const handleFilterChange = () => {
    onFilterChange?.({
      province: selectedProvince,
      priceRange,
      type: selectedType
    });
  };

  useEffect(() => {
    handleFilterChange();
  }, [selectedProvince, priceRange, selectedType]);

  return (
    <div className="space-y-4">
      {/* Map Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-wrap gap-4">
            <div className="flex-1 min-w-[200px]">
              <Select value={selectedProvince} onValueChange={setSelectedProvince}>
                <SelectTrigger>
                  <SelectValue placeholder="Select Province" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Provinces</SelectItem>
                  {SA_PROVINCES.map(province => (
                    <SelectItem key={province} value={province}>
                      {province}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1 min-w-[200px]">
              <Select value={priceRange} onValueChange={setPriceRange}>
                <SelectTrigger>
                  <SelectValue placeholder="Price Range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Prices</SelectItem>
                  <SelectItem value="0-500">R0 - R500</SelectItem>
                  <SelectItem value="500-1000">R500 - R1,000</SelectItem>
                  <SelectItem value="1000-1500">R1,000 - R1,500</SelectItem>
                  <SelectItem value="1500-2000">R1,500+</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex-1 min-w-[200px]">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue placeholder="Accommodation Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="camping">Camping</SelectItem>
                  <SelectItem value="glamping">Glamping</SelectItem>
                  <SelectItem value="chalet">Chalet</SelectItem>
                  <SelectItem value="caravan">Caravan Park</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Interactive Map */}
      <div className="relative">
        <Card>
          <CardContent className="p-0">
            <div className="relative h-96 bg-muted rounded-lg overflow-hidden">
              {!mapLoaded ? (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center">
                    <div className="w-8 h-8 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                    <p className="text-sm text-muted-foreground">Loading interactive map...</p>
                  </div>
                </div>
              ) : (
                <>
                  {/* Mock Map Background */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
                    <div className="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMSIgZmlsbD0iIzMzNzNkYyIgZmlsbC1vcGFjaXR5PSIwLjEiLz4KPC9zdmc+')] opacity-50"></div>
                  </div>
                  
                  {/* Campsite Markers */}
                  {filteredCampsites.map((campsite, index) => {
                    const province = campsite.province as keyof typeof provinceCoordinates;
                    const coords = provinceCoordinates[province];
                    if (!coords) return null;
                    
                    // Add some random offset for visualization
                    const offsetX = (Math.random() - 0.5) * 100;
                    const offsetY = (Math.random() - 0.5) * 60;
                    
                    return (
                      <div
                        key={campsite.id}
                        className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer"
                        style={{
                          left: `${50 + offsetX}px`,
                          top: `${50 + (index * 30) + offsetY}px`
                        }}
                        onMouseEnter={() => setHoveredCampsite(campsite.id)}
                        onMouseLeave={() => setHoveredCampsite(null)}
                      >
                        <div className={`bg-primary text-primary-foreground rounded-full p-2 shadow-lg transition-all ${
                          hoveredCampsite === campsite.id ? 'scale-110 z-10' : ''
                        }`}>
                          <MapPin className="h-4 w-4" />
                        </div>
                        
                        {/* Campsite Info Popup */}
                        {hoveredCampsite === campsite.id && (
                          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 z-20">
                            <Card className="shadow-lg">
                              <CardContent className="p-3">
                                <div className="flex gap-3">
                                  <img 
                                    src={campsite.image} 
                                    alt={campsite.name}
                                    className="w-16 h-16 rounded object-cover"
                                  />
                                  <div className="flex-1 min-w-0">
                                    <h4 className="font-semibold text-sm truncate">{campsite.name}</h4>
                                    <p className="text-xs text-muted-foreground">{campsite.location}</p>
                                    <div className="flex items-center justify-between mt-1">
                                      <div className="flex items-center">
                                        <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
                                        <span className="text-xs">{campsite.rating}</span>
                                      </div>
                                      <span className="text-sm font-semibold">R{campsite.price}/night</span>
                                    </div>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          </div>
                        )}
                      </div>
                    );
                  })}
                </>
              )}
            </div>
          </CardContent>
        </Card>
        
        {/* Map Legend */}
        <div className="absolute top-4 right-4 bg-card/90 backdrop-blur-sm rounded-lg p-3">
          <div className="text-xs font-medium mb-2">Legend</div>
          <div className="flex items-center text-xs">
            <div className="w-3 h-3 bg-primary rounded-full mr-2"></div>
            Campsite ({filteredCampsites.length})
          </div>
        </div>
      </div>

      {/* Results Summary */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-muted-foreground">
                Showing {filteredCampsites.length} campsite{filteredCampsites.length !== 1 ? 's' : ''} 
                {selectedProvince && selectedProvince !== 'all' && ` in ${selectedProvince}`}
              </p>
            </div>
            <Button size="sm" variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              More Filters
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MapSearch;