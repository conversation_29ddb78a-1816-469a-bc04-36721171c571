import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Crown, 
  Check, 
  X, 
  Star,
  Zap,
  Shield,
  Gift,
  Calendar,
  MapPin,
  Users,
  Percent,
  Bell,
  Headphones,
  Award,
  Sparkles,
  Search,
  Heart
} from 'lucide-react';

const Membership = () => {
  const [selectedPlan, setSelectedPlan] = useState<'free' | 'premium'>('premium');

  const features = {
    free: [
      { name: 'Browse all campsites', included: true, icon: MapPin },
      { name: 'Basic search & filters', included: true, icon: Search },
      { name: 'Read reviews & ratings', included: true, icon: Star },
      { name: 'Save up to 10 favorites', included: true, icon: Heart },
      { name: 'Community forum access', included: true, icon: Users },
      { name: 'Basic customer support', included: true, icon: Headphones },
      { name: 'Advanced search filters', included: false, icon: Zap },
      { name: 'Unlimited favorites', included: false, icon: Heart },
      { name: 'Exclusive discounts (10-25%)', included: false, icon: Percent },
      { name: 'Early booking access', included: false, icon: Calendar },
      { name: 'Featured campsite highlights', included: false, icon: Star },
      { name: 'Priority customer support', included: false, icon: Shield },
      { name: 'Monthly deals newsletter', included: false, icon: Bell },
      { name: 'Trip planning assistance', included: false, icon: MapPin },
      { name: 'Loyalty rewards program', included: false, icon: Award }
    ],
    premium: [
      { name: 'Browse all campsites', included: true, icon: MapPin },
      { name: 'Advanced search & filters', included: true, icon: Zap },
      { name: 'Read reviews & ratings', included: true, icon: Star },
      { name: 'Unlimited favorites', included: true, icon: Heart },
      { name: 'Community forum access', included: true, icon: Users },
      { name: 'Priority customer support', included: true, icon: Shield },
      { name: 'Exclusive discounts (10-25%)', included: true, icon: Percent },
      { name: 'Early booking access', included: true, icon: Calendar },
      { name: 'Featured campsite highlights', included: true, icon: Star },
      { name: 'Monthly deals newsletter', included: true, icon: Bell },
      { name: 'Trip planning assistance', included: true, icon: MapPin },
      { name: 'Loyalty rewards program', included: true, icon: Award },
      { name: 'Ad-free browsing experience', included: true, icon: Sparkles },
      { name: 'Exclusive member events', included: true, icon: Calendar },
      { name: 'Premium badge on profile', included: true, icon: Crown }
    ]
  };

  const testimonials = [
    {
      name: 'Sarah Mitchell',
      location: 'Cape Town',
      avatar: '👩‍🦰',
      quote: 'The premium membership has saved me hundreds on camping trips. The exclusive discounts and early access are worth every penny!',
      rating: 5
    },
    {
      name: 'David Thompson',
      location: 'Johannesburg',
      avatar: '👨‍🦲',
      quote: 'Love the trip planning assistance and priority support. Makes organizing family camping trips so much easier.',
      rating: 5
    },
    {
      name: 'Lisa van der Merwe',
      location: 'Durban',
      avatar: '👩‍🦱',
      quote: 'The member-only events have introduced me to amazing camping spots I never would have found otherwise.',
      rating: 5
    }
  ];

  const memberBenefits = [
    {
      icon: Percent,
      title: 'Exclusive Discounts',
      description: 'Save 10-25% on bookings at participating campsites',
      color: 'text-green-600'
    },
    {
      icon: Calendar,
      title: 'Early Access',
      description: 'Book popular campsites before they open to the public',
      color: 'text-blue-600'
    },
    {
      icon: Star,
      title: 'Featured Highlights',
      description: 'Get personalized recommendations based on your preferences',
      color: 'text-yellow-600'
    },
    {
      icon: Shield,
      title: 'Priority Support',
      description: '24/7 premium customer support with faster response times',
      color: 'text-purple-600'
    },
    {
      icon: Gift,
      title: 'Member Rewards',
      description: 'Earn points on every booking and redeem for free nights',
      color: 'text-pink-600'
    },
    {
      icon: Users,
      title: 'Exclusive Events',
      description: 'Access to member-only camping events and meetups',
      color: 'text-indigo-600'
    }
  ];

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <Crown className="h-8 w-8 text-yellow-500" />
            <h1 className="text-4xl font-bold">CampSpot Premium</h1>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
            Unlock exclusive benefits, save money, and enhance your camping experience with our premium membership
          </p>
          
          <div className="flex items-center justify-center gap-4 mb-8">
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              <Gift className="h-3 w-3 mr-1" />
              30-Day Free Trial
            </Badge>
            <Badge variant="secondary" className="bg-blue-100 text-blue-800">
              <Shield className="h-3 w-3 mr-1" />
              Cancel Anytime
            </Badge>
            <Badge variant="secondary" className="bg-purple-100 text-purple-800">
              <Star className="h-3 w-3 mr-1" />
              1000+ Happy Members
            </Badge>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16 max-w-4xl mx-auto">
          {/* Free Plan */}
          <Card className="relative">
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-2xl mb-2">Free</CardTitle>
              <div className="text-4xl font-bold mb-2">R0</div>
              <div className="text-muted-foreground">per month</div>
              <p className="text-sm text-muted-foreground mt-4">
                Perfect for occasional campers
              </p>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                {features.free.slice(0, 6).map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className={`p-1 rounded-full ${feature.included ? 'bg-green-100' : 'bg-gray-100'}`}>
                      {feature.included ? (
                        <Check className="h-3 w-3 text-green-600" />
                      ) : (
                        <X className="h-3 w-3 text-gray-400" />
                      )}
                    </div>
                    <span className={feature.included ? 'text-foreground' : 'text-muted-foreground'}>
                      {feature.name}
                    </span>
                  </li>
                ))}
              </ul>
              <Button variant="outline" className="w-full">
                Current Plan
              </Button>
            </CardContent>
          </Card>

          {/* Premium Plan */}
          <Card className="relative border-2 border-primary">
            <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-primary text-primary-foreground px-4 py-1">
                <Crown className="h-3 w-3 mr-1" />
                Most Popular
              </Badge>
            </div>
            <CardHeader className="text-center pb-8">
              <CardTitle className="text-2xl mb-2 flex items-center justify-center gap-2">
                <Crown className="h-5 w-5 text-yellow-500" />
                Premium
              </CardTitle>
              <div className="text-4xl font-bold mb-2">R99</div>
              <div className="text-muted-foreground">per month</div>
              <p className="text-sm text-muted-foreground mt-4">
                For serious camping enthusiasts
              </p>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 mb-8">
                {features.premium.slice(0, 8).map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <div className="p-1 rounded-full bg-green-100">
                      <Check className="h-3 w-3 text-green-600" />
                    </div>
                    <span className="text-foreground">{feature.name}</span>
                  </li>
                ))}
                <li className="text-sm text-muted-foreground">+ 7 more premium features</li>
              </ul>
              <Button className="w-full bg-primary hover:bg-primary/90">
                Start Free Trial
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Member Benefits */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Premium Member Benefits</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {memberBenefits.map((benefit, index) => (
              <Card key={index} className="text-center p-6">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-muted mb-4`}>
                  <benefit.icon className={`h-6 w-6 ${benefit.color}`} />
                </div>
                <h3 className="text-lg font-semibold mb-2">{benefit.title}</h3>
                <p className="text-muted-foreground">{benefit.description}</p>
              </Card>
            ))}
          </div>
        </div>

        {/* Feature Comparison */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">Complete Feature Comparison</h2>
          <Card className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-muted">
                  <tr>
                    <th className="text-left p-4 font-semibold">Features</th>
                    <th className="text-center p-4 font-semibold">Free</th>
                    <th className="text-center p-4 font-semibold">
                      <div className="flex items-center justify-center gap-1">
                        <Crown className="h-4 w-4 text-yellow-500" />
                        Premium
                      </div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {features.premium.map((feature, index) => {
                    const freeFeature = features.free.find(f => f.name === feature.name);
                    return (
                      <tr key={index} className="border-b">
                        <td className="p-4 flex items-center gap-2">
                          <feature.icon className="h-4 w-4 text-muted-foreground" />
                          {feature.name}
                        </td>
                        <td className="text-center p-4">
                          {freeFeature?.included ? (
                            <Check className="h-5 w-5 text-green-600 mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-gray-400 mx-auto" />
                          )}
                        </td>
                        <td className="text-center p-4">
                          <Check className="h-5 w-5 text-green-600 mx-auto" />
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </Card>
        </div>

        {/* Testimonials */}
        <div className="mb-16">
          <h2 className="text-3xl font-bold text-center mb-12">What Our Premium Members Say</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="text-2xl">{testimonial.avatar}</div>
                  <div>
                    <div className="font-semibold">{testimonial.name}</div>
                    <div className="text-sm text-muted-foreground">{testimonial.location}</div>
                  </div>
                </div>
                <div className="flex items-center gap-1 mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-4 w-4 text-yellow-500 fill-current" />
                  ))}
                </div>
                <p className="text-muted-foreground italic">"{testimonial.quote}"</p>
              </Card>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-primary/5 rounded-lg p-12">
          <Crown className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
          <h2 className="text-3xl font-bold mb-4">Ready to Upgrade Your Camping Experience?</h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of happy campers who save money and discover amazing destinations with CampSpot Premium
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" className="bg-primary hover:bg-primary/90">
              <Crown className="h-4 w-4 mr-2" />
              Start 30-Day Free Trial
            </Button>
            <Button size="lg" variant="outline">
              Learn More
            </Button>
          </div>
          <p className="text-sm text-muted-foreground mt-4">
            No credit card required • Cancel anytime • Full refund within 30 days
          </p>
        </div>
      </div>
    </div>
  );
};

export default Membership;
