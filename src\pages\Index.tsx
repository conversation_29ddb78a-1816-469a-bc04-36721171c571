import { Navigation } from "@/components/ui/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Star, Tent, MapPin, Map, Calendar } from "lucide-react";
import { allCampsites } from "@/data/mock-campsites";
import BlogSection from "@/components/BlogSection";
import heroBackground from "@/assets/hero-background.png";

const Index = () => {
  const featuredCampsites = allCampsites.slice(0, 3);

  return (
    <div className="min-h-screen bg-background">
      <Navigation />

      {/* Hero Section */}
      <section 
        className="relative py-20 px-4 min-h-[70vh] flex items-center justify-center bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.6)), url(${heroBackground})`
        }}
      >
        <div className="container mx-auto text-center relative z-10">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
            Find your perfect campsite in <span className="text-yellow-300">South Africa</span>
          </h1>
          <p className="text-xl text-white/90 mb-8">
            Discover incredible camping experiences across the Rainbow Nation
          </p>
          <Button size="lg" variant="secondary">
            Start Exploring
          </Button>
        </div>
      </section>

      {/* Featured Campsites */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Featured Campsites</h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {featuredCampsites.map((campsite) => (
              <Card key={campsite.id} className="overflow-hidden">
                <div className="relative h-48 bg-muted">
                  <img
                    src={campsite.image}
                    alt={campsite.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 right-4">
                    <div className="bg-white/90 rounded-full px-3 py-1">
                      <span className="text-sm font-semibold">
                        R{campsite.price}/night
                      </span>
                    </div>
                  </div>
                </div>

                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-2">
                    <h3 className="text-lg font-semibold">
                      {campsite.name}
                    </h3>
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                      <span className="text-sm text-muted-foreground ml-1">
                        {campsite.rating}
                      </span>
                    </div>
                  </div>

                  <p className="text-muted-foreground text-sm mb-4 flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {campsite.location}, {campsite.province}
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {campsite.amenities.slice(0, 3).map((amenity) => (
                      <span
                        key={amenity}
                        className="inline-flex items-center px-2 py-1 bg-secondary text-secondary-foreground text-xs rounded-full"
                      >
                        {amenity}
                      </span>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button size="lg" asChild>
              <a href="/search">View All Campsites</a>
            </Button>
          </div>
        </div>
      </section>

      {/* Blog Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <BlogSection />
        </div>
      </section>
    </div>
  );
};

export default Index;
