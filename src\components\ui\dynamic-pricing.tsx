import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { 
  Calendar as CalendarIcon, 
  TrendingUp, 
  TrendingDown,
  Info
} from 'lucide-react';
import { format, addDays, isWeekend, isSameDay } from 'date-fns';
import { Campsite } from '@/data';

interface DynamicPricingProps {
  campsite: Campsite;
  selectedDate?: Date;
  onDateSelect?: (date: Date) => void;
}

export function DynamicPricing({ campsite, selectedDate, onDateSelect }: DynamicPricingProps) {
  const [showCalendar, setShowCalendar] = useState(false);

  // Mock seasonal pricing data
  const getSeasonalPricing = (date: Date) => {
    const month = date.getMonth();
    const basePrice = campsite.price;
    
    // South African seasons (opposite to Northern Hemisphere)
    // Summer: Dec-Feb (peak season)
    // Autumn: Mar-May 
    // Winter: Jun-Aug (low season)
    // Spring: Sep-Nov
    
    let multiplier = 1;
    let season = 'Standard';
    let demand = 'normal';
    
    if (month >= 11 || month <= 1) { // Summer (Dec-Feb)
      multiplier = 1.4;
      season = 'Summer Peak';
      demand = 'high';
    } else if (month >= 2 && month <= 4) { // Autumn (Mar-May)
      multiplier = 1.1;
      season = 'Autumn';
      demand = 'normal';
    } else if (month >= 5 && month <= 7) { // Winter (Jun-Aug)
      multiplier = 0.8;
      season = 'Winter Low';
      demand = 'low';
    } else { // Spring (Sep-Nov)
      multiplier = 1.2;
      season = 'Spring';
      demand = 'normal';
    }
    
    // Weekend premium
    if (isWeekend(date)) {
      multiplier += 0.2;
    }
    
    // Holiday periods (mock data)
    const holidays = [
      { start: new Date(2024, 11, 16), end: new Date(2025, 0, 15), name: 'Summer Holidays', premium: 0.5 },
      { start: new Date(2024, 2, 25), end: new Date(2024, 3, 2), name: 'Easter Holidays', premium: 0.3 },
      { start: new Date(2024, 5, 16), end: new Date(2024, 6, 15), name: 'Winter Holidays', premium: 0.2 },
      { start: new Date(2024, 8, 23), end: new Date(2024, 8, 30), name: 'Heritage Day', premium: 0.25 }
    ];
    
    for (const holiday of holidays) {
      if (date >= holiday.start && date <= holiday.end) {
        multiplier += holiday.premium;
        season = holiday.name;
        demand = 'very-high';
        break;
      }
    }
    
    const finalPrice = Math.round(basePrice * multiplier);
    
    return {
      price: finalPrice,
      basePrice,
      multiplier,
      season,
      demand,
      savings: basePrice - finalPrice,
      isWeekend: isWeekend(date)
    };
  };

  const currentDate = selectedDate || new Date();
  const pricing = getSeasonalPricing(currentDate);
  
  // Get pricing for next 7 days
  const weekPricing = Array.from({ length: 7 }, (_, i) => {
    const date = addDays(new Date(), i);
    return {
      date,
      ...getSeasonalPricing(date)
    };
  });

  const getDemandColor = (demand: string) => {
    switch (demand) {
      case 'very-high': return 'bg-red-100 text-red-800';
      case 'high': return 'bg-orange-100 text-orange-800';
      case 'normal': return 'bg-blue-100 text-blue-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriceChangeIcon = (savings: number) => {
    if (savings > 0) return <TrendingDown className="h-4 w-4 text-green-500" />;
    if (savings < 0) return <TrendingUp className="h-4 w-4 text-red-500" />;
    return null;
  };

  return (
    <div className="space-y-4">
      {/* Current Date Pricing */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Dynamic Pricing</CardTitle>
            <Popover open={showCalendar} onOpenChange={setShowCalendar}>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  {format(currentDate, "MMM dd")}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={currentDate}
                  onSelect={(date) => {
                    if (date) {
                      onDateSelect?.(date);
                      setShowCalendar(false);
                    }
                  }}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-2xl font-bold">R{pricing.price}</span>
                  {pricing.savings !== 0 && getPriceChangeIcon(pricing.savings)}
                  {pricing.savings > 0 && (
                    <span className="text-sm text-green-600 font-medium">
                      Save R{pricing.savings}
                    </span>
                  )}
                  {pricing.savings < 0 && (
                    <span className="text-sm text-red-600 font-medium">
                      +R{Math.abs(pricing.savings)}
                    </span>
                  )}
                </div>
                <div className="text-sm text-muted-foreground">
                  {pricing.basePrice !== pricing.price && (
                    <span className="line-through mr-2">R{pricing.basePrice}</span>
                  )}
                  per night
                </div>
              </div>
              
              <div className="text-right">
                <Badge className={getDemandColor(pricing.demand)}>
                  {pricing.demand.replace('-', ' ')} demand
                </Badge>
                <div className="text-sm text-muted-foreground mt-1">
                  {pricing.season}
                </div>
              </div>
            </div>
            
            {pricing.isWeekend && (
              <div className="flex items-center gap-2 text-sm text-blue-600 bg-blue-50 p-2 rounded">
                <Info className="h-4 w-4" />
                Weekend premium applied
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 7-Day Price Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Price Forecast</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-2">
            {weekPricing.map((day, index) => (
              <div 
                key={index}
                className={`text-center p-2 rounded-lg border cursor-pointer transition-colors ${
                  isSameDay(day.date, currentDate) 
                    ? 'bg-primary text-primary-foreground' 
                    : 'hover:bg-muted'
                }`}
                onClick={() => onDateSelect?.(day.date)}
              >
                <div className="text-xs font-medium mb-1">
                  {format(day.date, 'EEE')}
                </div>
                <div className="text-xs text-muted-foreground mb-1">
                  {format(day.date, 'dd')}
                </div>
                <div className="text-sm font-semibold">
                  R{day.price}
                </div>
                {day.demand === 'very-high' && (
                  <div className="w-2 h-2 bg-red-500 rounded-full mx-auto mt-1"></div>
                )}
                {day.demand === 'high' && (
                  <div className="w-2 h-2 bg-orange-500 rounded-full mx-auto mt-1"></div>
                )}
                {day.demand === 'low' && (
                  <div className="w-2 h-2 bg-green-500 rounded-full mx-auto mt-1"></div>
                )}
              </div>
            ))}
          </div>
          
          <div className="flex items-center justify-center gap-4 mt-4 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-red-500 rounded-full"></div>
              Very High
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
              High
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              Low
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Pricing Factors */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Pricing Factors</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex justify-between">
              <span>Base Price:</span>
              <span>R{pricing.basePrice}</span>
            </div>
            <div className="flex justify-between">
              <span>Seasonal Adjustment:</span>
              <span className={pricing.multiplier > 1 ? 'text-red-600' : pricing.multiplier < 1 ? 'text-green-600' : ''}>
                {pricing.multiplier > 1 ? '+' : ''}{((pricing.multiplier - 1) * 100).toFixed(0)}%
              </span>
            </div>
            {pricing.isWeekend && (
              <div className="flex justify-between">
                <span>Weekend Premium:</span>
                <span className="text-red-600">+20%</span>
              </div>
            )}
            <hr />
            <div className="flex justify-between font-semibold">
              <span>Final Price:</span>
              <span>R{pricing.price}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
