import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  CreditCard, 
  Smartphone, 
  Building, 
  Shield, 
  Check,
  AlertCircle 
} from 'lucide-react';

interface PaymentOptionsProps {
  amount: number;
  onPaymentSelect?: (method: string) => void;
}

const PaymentOptions = ({ amount, onPaymentSelect }: PaymentOptionsProps) => {
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [cardDetails, setCardDetails] = useState({
    number: '',
    expiry: '',
    cvv: '',
    name: ''
  });

  const paymentMethods = [
    {
      id: 'payfast',
      name: 'PayFast',
      description: 'Secure online payment',
      icon: CreditCard,
      fee: 0,
      recommended: true
    },
    {
      id: 'ozow',
      name: 'Ozow',
      description: 'Instant bank transfer',
      icon: Building,
      fee: 0,
      recommended: false
    },
    {
      id: 'snapscan',
      name: 'SnapScan',
      description: 'QR code payment',
      icon: Smartphone,
      fee: 0,
      recommended: false
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      description: 'Visa, Mastercard',
      icon: CreditCard,
      fee: amount * 0.029, // 2.9% fee
      recommended: false
    }
  ];

  const handleMethodSelect = (methodId: string) => {
    setSelectedMethod(methodId);
    onPaymentSelect?.(methodId);
  };

  const calculateTotal = () => {
    const method = paymentMethods.find(m => m.id === selectedMethod);
    return amount + (method?.fee || 0);
  };

  const handlePayment = () => {
    // Mock payment processing
    alert(`Processing payment of R${calculateTotal().toFixed(2)} via ${selectedMethod}`);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Payment Method
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Payment Amount Summary */}
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <span>Subtotal</span>
            <span>R{amount.toFixed(2)}</span>
          </div>
          {selectedMethod && paymentMethods.find(m => m.id === selectedMethod)?.fee! > 0 && (
            <div className="flex justify-between items-center mb-2 text-sm">
              <span>Processing fee</span>
              <span>R{paymentMethods.find(m => m.id === selectedMethod)?.fee?.toFixed(2)}</span>
            </div>
          )}
          <Separator className="my-2" />
          <div className="flex justify-between items-center font-semibold">
            <span>Total</span>
            <span>R{calculateTotal().toFixed(2)}</span>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="space-y-3">
          <Label className="text-base font-medium">Choose Payment Method</Label>
          
          {paymentMethods.map(method => {
            const Icon = method.icon;
            const isSelected = selectedMethod === method.id;
            
            return (
              <div
                key={method.id}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  isSelected 
                    ? 'border-primary bg-primary/5' 
                    : 'border-border hover:border-muted-foreground'
                }`}
                onClick={() => handleMethodSelect(method.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      isSelected ? 'bg-primary text-primary-foreground' : 'bg-muted'
                    }`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{method.name}</span>
                        {method.recommended && (
                          <Badge variant="secondary" className="text-xs">
                            Recommended
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-muted-foreground">{method.description}</p>
                      {method.fee > 0 && (
                        <p className="text-xs text-orange-600">
                          + R{method.fee.toFixed(2)} processing fee
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {isSelected && (
                    <div className="w-5 h-5 rounded-full bg-primary flex items-center justify-center">
                      <Check className="h-3 w-3 text-primary-foreground" />
                    </div>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Card Details Form (only show for card payment) */}
        {selectedMethod === 'card' && (
          <div className="space-y-4 border-t pt-6">
            <Label className="text-base font-medium">Card Details</Label>
            
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="cardName">Cardholder Name</Label>
                <Input
                  id="cardName"
                  placeholder="Full name on card"
                  value={cardDetails.name}
                  onChange={(e) => setCardDetails(prev => ({ ...prev, name: e.target.value }))}
                />
              </div>
              
              <div>
                <Label htmlFor="cardNumber">Card Number</Label>
                <Input
                  id="cardNumber"
                  placeholder="1234 5678 9012 3456"
                  value={cardDetails.number}
                  onChange={(e) => setCardDetails(prev => ({ ...prev, number: e.target.value }))}
                />
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="expiry">Expiry Date</Label>
                  <Input
                    id="expiry"
                    placeholder="MM/YY"
                    value={cardDetails.expiry}
                    onChange={(e) => setCardDetails(prev => ({ ...prev, expiry: e.target.value }))}
                  />
                </div>
                <div>
                  <Label htmlFor="cvv">CVV</Label>
                  <Input
                    id="cvv"
                    placeholder="123"
                    value={cardDetails.cvv}
                    onChange={(e) => setCardDetails(prev => ({ ...prev, cvv: e.target.value }))}
                  />
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Security Notice */}
        <div className="flex items-start gap-2 p-3 bg-blue-50 rounded-lg">
          <Shield className="h-4 w-4 text-blue-600 mt-0.5" />
          <div className="text-sm">
            <p className="font-medium text-blue-900">Secure Payment</p>
            <p className="text-blue-700">
              Your payment information is encrypted and processed securely through our trusted South African payment partners.
            </p>
          </div>
        </div>

        {/* Payment Button */}
        <Button 
          className="w-full" 
          size="lg" 
          disabled={!selectedMethod}
          onClick={handlePayment}
        >
          {selectedMethod ? (
            <>Pay R{calculateTotal().toFixed(2)} with {paymentMethods.find(m => m.id === selectedMethod)?.name}</>
          ) : (
            'Select Payment Method'
          )}
        </Button>

        {/* Payment Methods Logos */}
        <div className="text-center">
          <p className="text-xs text-muted-foreground mb-2">Powered by trusted South African payment providers</p>
          <div className="flex justify-center items-center gap-4 grayscale opacity-60">
            <div className="text-xs font-bold border px-2 py-1 rounded">PayFast</div>
            <div className="text-xs font-bold border px-2 py-1 rounded">Ozow</div>
            <div className="text-xs font-bold border px-2 py-1 rounded">SnapScan</div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PaymentOptions;