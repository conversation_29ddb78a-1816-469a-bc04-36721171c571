import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Send, 
  Search, 
  MoreHorizontal,
  Phone,
  Video,
  Info,
  Paperclip,
  Smile,
  Check,
  CheckCheck
} from 'lucide-react';
import { mockUsers, allCampsites } from '@/data';

interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  read: boolean;
  type: 'text' | 'image' | 'booking';
  bookingId?: string;
}

interface Conversation {
  id: string;
  participants: string[];
  lastMessage: Message;
  unreadCount: number;
  campsiteId?: string;
}

const Messages = () => {
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);
  const [newMessage, setNewMessage] = useState('');
  const [searchQuery, setSearchQuery] = useState('');

  // Mock current user
  const currentUser = mockUsers[0]; // Sarah Johnson

  // Mock conversations
  const mockConversations: Conversation[] = [
    {
      id: 'conv-1',
      participants: [currentUser.id, mockUsers[1].id], // Sarah & John
      lastMessage: {
        id: 'msg-1',
        senderId: mockUsers[1].id,
        receiverId: currentUser.id,
        content: 'Thanks for your interest in Mountain View Campsite! The site is available for your dates.',
        timestamp: '2024-01-15T10:30:00Z',
        read: false,
        type: 'text'
      },
      unreadCount: 2,
      campsiteId: '1'
    },
    {
      id: 'conv-2',
      participants: [currentUser.id, mockUsers[2].id], // Sarah & Emma
      lastMessage: {
        id: 'msg-2',
        senderId: currentUser.id,
        receiverId: mockUsers[2].id,
        content: 'Perfect! Looking forward to our stay at Riverside Retreat.',
        timestamp: '2024-01-14T15:45:00Z',
        read: true,
        type: 'text'
      },
      unreadCount: 0,
      campsiteId: '2'
    },
    {
      id: 'conv-3',
      participants: [currentUser.id, mockUsers[3].id], // Sarah & Michael
      lastMessage: {
        id: 'msg-3',
        senderId: mockUsers[3].id,
        receiverId: currentUser.id,
        content: 'Your booking for Desert Oasis has been confirmed!',
        timestamp: '2024-01-13T09:15:00Z',
        read: true,
        type: 'booking'
      },
      unreadCount: 0,
      campsiteId: '3'
    }
  ];

  // Mock messages for selected conversation
  const mockMessages: Message[] = [
    {
      id: 'msg-1a',
      senderId: currentUser.id,
      receiverId: mockUsers[1].id,
      content: 'Hi! I\'m interested in booking Mountain View Campsite for December 15-18. Is it available?',
      timestamp: '2024-01-15T09:00:00Z',
      read: true,
      type: 'text'
    },
    {
      id: 'msg-1b',
      senderId: mockUsers[1].id,
      receiverId: currentUser.id,
      content: 'Hello Sarah! Yes, those dates are available. The site can accommodate up to 6 people and includes all the amenities listed.',
      timestamp: '2024-01-15T09:15:00Z',
      read: true,
      type: 'text'
    },
    {
      id: 'msg-1c',
      senderId: mockUsers[1].id,
      receiverId: currentUser.id,
      content: 'Thanks for your interest in Mountain View Campsite! The site is available for your dates.',
      timestamp: '2024-01-15T10:30:00Z',
      read: false,
      type: 'text'
    },
    {
      id: 'msg-1d',
      senderId: mockUsers[1].id,
      receiverId: currentUser.id,
      content: 'Would you like me to send you some additional photos of the site?',
      timestamp: '2024-01-15T10:32:00Z',
      read: false,
      type: 'text'
    }
  ];

  const selectedConv = mockConversations.find(c => c.id === selectedConversation);
  const otherParticipant = selectedConv ? mockUsers.find(u => 
    selectedConv.participants.includes(u.id) && u.id !== currentUser.id
  ) : null;
  const campsite = selectedConv?.campsiteId ? allCampsites.find(c => c.id === selectedConv.campsiteId) : null;

  const handleSendMessage = () => {
    if (!newMessage.trim() || !selectedConversation) return;
    
    console.log('Sending message:', newMessage);
    setNewMessage('');
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
    
    if (diffInHours < 24) {
      return date.toLocaleTimeString('en-ZA', { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString('en-ZA', { month: 'short', day: 'numeric' });
    }
  };

  const filteredConversations = mockConversations.filter(conv => {
    if (!searchQuery) return true;
    const otherUser = mockUsers.find(u => 
      conv.participants.includes(u.id) && u.id !== currentUser.id
    );
    return otherUser?.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
           conv.lastMessage.content.toLowerCase().includes(searchQuery.toLowerCase());
  });

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-200px)]">
          {/* Conversations List */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle>Messages</CardTitle>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search conversations..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <ScrollArea className="h-[500px]">
                <div className="space-y-1">
                  {filteredConversations.map(conversation => {
                    const otherUser = mockUsers.find(u => 
                      conversation.participants.includes(u.id) && u.id !== currentUser.id
                    );
                    const conversationCampsite = conversation.campsiteId ? 
                      allCampsites.find(c => c.id === conversation.campsiteId) : null;
                    
                    return otherUser && (
                      <div
                        key={conversation.id}
                        className={`flex items-center gap-3 p-4 cursor-pointer hover:bg-muted/50 transition-colors ${
                          selectedConversation === conversation.id ? 'bg-muted' : ''
                        }`}
                        onClick={() => setSelectedConversation(conversation.id)}
                      >
                        <div className="relative">
                          <Avatar>
                            <AvatarImage src={otherUser.avatar} />
                            <AvatarFallback>{otherUser.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          {conversation.unreadCount > 0 && (
                            <div className="absolute -top-1 -right-1 bg-primary text-primary-foreground text-xs rounded-full h-5 w-5 flex items-center justify-center">
                              {conversation.unreadCount}
                            </div>
                          )}
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between mb-1">
                            <h3 className="font-semibold truncate">{otherUser.name}</h3>
                            <span className="text-xs text-muted-foreground">
                              {formatTime(conversation.lastMessage.timestamp)}
                            </span>
                          </div>
                          
                          {conversationCampsite && (
                            <div className="text-xs text-muted-foreground mb-1">
                              {conversationCampsite.name}
                            </div>
                          )}
                          
                          <p className={`text-sm truncate ${
                            conversation.unreadCount > 0 ? 'font-medium' : 'text-muted-foreground'
                          }`}>
                            {conversation.lastMessage.type === 'booking' ? '📅 ' : ''}
                            {conversation.lastMessage.content}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>

          {/* Chat Area */}
          <Card className="lg:col-span-2">
            {selectedConversation && otherParticipant ? (
              <>
                {/* Chat Header */}
                <CardHeader className="border-b">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={otherParticipant.avatar} />
                        <AvatarFallback>{otherParticipant.name.charAt(0)}</AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{otherParticipant.name}</h3>
                        {campsite && (
                          <p className="text-sm text-muted-foreground">{campsite.name}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button variant="ghost" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Video className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Info className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>

                {/* Messages */}
                <CardContent className="p-0">
                  <ScrollArea className="h-[400px] p-4">
                    <div className="space-y-4">
                      {mockMessages.map(message => {
                        const isCurrentUser = message.senderId === currentUser.id;
                        const sender = mockUsers.find(u => u.id === message.senderId);
                        
                        return (
                          <div
                            key={message.id}
                            className={`flex ${isCurrentUser ? 'justify-end' : 'justify-start'}`}
                          >
                            <div className={`flex gap-2 max-w-[70%] ${isCurrentUser ? 'flex-row-reverse' : ''}`}>
                              {!isCurrentUser && (
                                <Avatar className="h-8 w-8">
                                  <AvatarImage src={sender?.avatar} />
                                  <AvatarFallback className="text-xs">
                                    {sender?.name.charAt(0)}
                                  </AvatarFallback>
                                </Avatar>
                              )}
                              
                              <div className={`rounded-lg p-3 ${
                                isCurrentUser 
                                  ? 'bg-primary text-primary-foreground' 
                                  : 'bg-muted'
                              }`}>
                                <p className="text-sm">{message.content}</p>
                                <div className={`flex items-center justify-end gap-1 mt-1 text-xs ${
                                  isCurrentUser ? 'text-primary-foreground/70' : 'text-muted-foreground'
                                }`}>
                                  <span>{formatTime(message.timestamp)}</span>
                                  {isCurrentUser && (
                                    message.read ? (
                                      <CheckCheck className="h-3 w-3" />
                                    ) : (
                                      <Check className="h-3 w-3" />
                                    )
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </ScrollArea>
                </CardContent>

                {/* Message Input */}
                <div className="border-t p-4">
                  <div className="flex items-center gap-2">
                    <Button variant="ghost" size="sm">
                      <Paperclip className="h-4 w-4" />
                    </Button>
                    <div className="flex-1 relative">
                      <Textarea
                        placeholder="Type a message..."
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        className="min-h-[40px] max-h-[120px] resize-none pr-12"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault();
                            handleSendMessage();
                          }
                        }}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                      >
                        <Smile className="h-4 w-4" />
                      </Button>
                    </div>
                    <Button 
                      onClick={handleSendMessage}
                      disabled={!newMessage.trim()}
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </>
            ) : (
              <CardContent className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="h-16 w-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
                    <Send className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">Select a conversation</h3>
                  <p className="text-muted-foreground">
                    Choose a conversation from the list to start messaging
                  </p>
                </div>
              </CardContent>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Messages;
