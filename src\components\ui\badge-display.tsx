import React from 'react';
import { Badge } from '@/components/ui/badge';
import { getBadgeById, getBadgesByIds, type Badge as BadgeType } from '@/data/badges';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface BadgeDisplayProps {
  badgeIds: string[];
  maxVisible?: number;
  size?: 'sm' | 'md' | 'lg';
  showTooltip?: boolean;
  className?: string;
}

export const BadgeDisplay: React.FC<BadgeDisplayProps> = ({
  badgeIds,
  maxVisible = 4,
  size = 'sm',
  showTooltip = true,
  className = ''
}) => {
  const badges = getBadgesByIds(badgeIds);
  const visibleBadges = badges.slice(0, maxVisible);
  const hiddenCount = badges.length - maxVisible;

  const getSizeClasses = (size: string) => {
    switch (size) {
      case 'sm':
        return 'text-xs px-2 py-1';
      case 'md':
        return 'text-sm px-3 py-1';
      case 'lg':
        return 'text-base px-4 py-2';
      default:
        return 'text-xs px-2 py-1';
    }
  };

  const BadgeComponent = ({ badge }: { badge: BadgeType }) => {
    const badgeElement = (
      <Badge
        variant="secondary"
        className={`${badge.bgColor} ${badge.color} border-0 ${getSizeClasses(size)} ${className}`}
      >
        <span className="mr-1">{badge.icon}</span>
        {badge.name}
      </Badge>
    );

    if (!showTooltip) {
      return badgeElement;
    }

    return (
      <TooltipProvider key={badge.id}>
        <Tooltip>
          <TooltipTrigger asChild>
            {badgeElement}
          </TooltipTrigger>
          <TooltipContent>
            <div className="max-w-xs">
              <div className="font-semibold">{badge.name}</div>
              <div className="text-sm text-muted-foreground">{badge.description}</div>
              <div className="text-xs text-muted-foreground mt-1 capitalize">
                {badge.category} • {badge.criteria}
              </div>
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  if (badges.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-1">
      {visibleBadges.map((badge) => (
        <BadgeComponent key={badge.id} badge={badge} />
      ))}
      
      {hiddenCount > 0 && (
        <Badge
          variant="outline"
          className={`${getSizeClasses(size)} text-muted-foreground`}
        >
          +{hiddenCount} more
        </Badge>
      )}
    </div>
  );
};

// Specialized badge displays for different contexts
export const CampsiteBadges: React.FC<{
  badgeIds: string[];
  featured?: boolean;
  compact?: boolean;
}> = ({ badgeIds, featured = false, compact = false }) => {
  return (
    <div className="space-y-2">
      {featured && (
        <Badge className="bg-primary text-primary-foreground">
          ⭐ Featured
        </Badge>
      )}
      <BadgeDisplay
        badgeIds={badgeIds}
        maxVisible={compact ? 3 : 6}
        size={compact ? 'sm' : 'md'}
        showTooltip={true}
      />
    </div>
  );
};

export const BadgeFilter: React.FC<{
  badges: BadgeType[];
  selectedBadges: string[];
  onBadgeToggle: (badgeId: string) => void;
  category?: string;
}> = ({ badges, selectedBadges, onBadgeToggle, category }) => {
  const filteredBadges = category 
    ? badges.filter(badge => badge.category === category)
    : badges;

  return (
    <div className="space-y-3">
      {category && (
        <h4 className="font-medium text-sm capitalize">{category} Badges</h4>
      )}
      <div className="flex flex-wrap gap-2">
        {filteredBadges.map((badge) => (
          <button
            key={badge.id}
            onClick={() => onBadgeToggle(badge.id)}
            className={`inline-flex items-center px-3 py-1 rounded-full text-sm transition-colors ${
              selectedBadges.includes(badge.id)
                ? `${badge.bgColor} ${badge.color} ring-2 ring-primary`
                : 'bg-muted text-muted-foreground hover:bg-muted/80'
            }`}
          >
            <span className="mr-1">{badge.icon}</span>
            {badge.name}
          </button>
        ))}
      </div>
    </div>
  );
};

// Badge legend/explanation component
export const BadgeLegend: React.FC<{
  badges: BadgeType[];
  groupByCategory?: boolean;
}> = ({ badges, groupByCategory = true }) => {
  if (!groupByCategory) {
    return (
      <div className="space-y-2">
        {badges.map((badge) => (
          <div key={badge.id} className="flex items-start gap-3 p-2 rounded-lg bg-muted/50">
            <div className={`inline-flex items-center px-2 py-1 rounded text-xs ${badge.bgColor} ${badge.color}`}>
              <span className="mr-1">{badge.icon}</span>
              {badge.name}
            </div>
            <div className="flex-1">
              <div className="text-sm">{badge.description}</div>
              <div className="text-xs text-muted-foreground">{badge.criteria}</div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  const groupedBadges = badges.reduce((acc, badge) => {
    if (!acc[badge.category]) {
      acc[badge.category] = [];
    }
    acc[badge.category].push(badge);
    return acc;
  }, {} as Record<string, BadgeType[]>);

  return (
    <div className="space-y-6">
      {Object.entries(groupedBadges).map(([category, categoryBadges]) => (
        <div key={category}>
          <h3 className="font-semibold mb-3 capitalize">{category} Badges</h3>
          <div className="space-y-2">
            {categoryBadges.map((badge) => (
              <div key={badge.id} className="flex items-start gap-3 p-2 rounded-lg bg-muted/50">
                <div className={`inline-flex items-center px-2 py-1 rounded text-xs ${badge.bgColor} ${badge.color}`}>
                  <span className="mr-1">{badge.icon}</span>
                  {badge.name}
                </div>
                <div className="flex-1">
                  <div className="text-sm">{badge.description}</div>
                  <div className="text-xs text-muted-foreground">{badge.criteria}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};
