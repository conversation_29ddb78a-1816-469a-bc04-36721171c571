import { useState } from 'react';
import { useParams } from 'react-router-dom';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { ReviewSummary } from '@/components/ui/review-summary';
import { ReviewList } from '@/components/ui/review-card';
import { WishlistButton } from '@/components/ui/wishlist-button';
import { DynamicPricing } from '@/components/ui/dynamic-pricing';
import { 
  Star, 
  MapPin, 
  Users, 
  Clock, 
  Wifi, 
  Zap, 
  Car,
  Waves,
  TreePine,
  Utensils,
  Mountain,
  Heart,
  Share2,
  Calendar,
  ThumbsUp,
  MessageSquare,
  CheckCircle
} from 'lucide-react';
import { 
  getCampsiteById, 
  getReviewsByCampsiteId, 
  getPromotionsByCampsiteId,
  getProviderById 
} from '@/data';

const CampsiteDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [showReviewDialog, setShowReviewDialog] = useState(false);
  const [newReview, setNewReview] = useState({ rating: 5, title: '', comment: '' });

  const campsite = getCampsiteById(id || '');
  const reviews = getReviewsByCampsiteId(id || '');
  const promotions = getPromotionsByCampsiteId(id || '');
  const provider = campsite ? getProviderById(campsite.providerId) : null;

  if (!campsite) {
    return (
      <div className="min-h-screen bg-background">
        <Navigation />
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-4">Campsite not found</h1>
            <Button asChild>
              <a href="/search">Back to Search</a>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const getAmenityIcon = (amenity: string) => {
    switch (amenity) {
      case 'Wi-Fi': return <Wifi className="h-4 w-4" />;
      case 'Electricity': return <Zap className="h-4 w-4" />;
      case 'Private Ablutions': return <Car className="h-4 w-4" />;
      case 'Swimming Pool': return <Waves className="h-4 w-4" />;
      case 'Hiking Trails': return <Mountain className="h-4 w-4" />;
      case 'Restaurant': return <Utensils className="h-4 w-4" />;
      case 'Braai Facilities': return <TreePine className="h-4 w-4" />;
      default: return null;
    }
  };

  const renderStars = (rating: number, interactive = false, onRatingChange?: (rating: number) => void) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            className={`h-5 w-5 ${
              star <= rating 
                ? 'fill-yellow-400 text-yellow-400' 
                : 'text-gray-300'
            } ${interactive ? 'cursor-pointer hover:text-yellow-400' : ''}`}
            onClick={interactive && onRatingChange ? () => onRatingChange(star) : undefined}
          />
        ))}
      </div>
    );
  };

  const handleSubmitReview = () => {
    // In a real app, this would submit to an API
    console.log('Submitting review:', newReview);
    setShowReviewDialog(false);
    setNewReview({ rating: 5, title: '', comment: '' });
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Hero Section */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
          {/* Main Image */}
          <div className="lg:col-span-2">
            <div className="relative h-96 rounded-lg overflow-hidden">
              <img 
                src={campsite.image} 
                alt={campsite.name}
                className="w-full h-full object-cover"
              />
              {campsite.featured && (
                <Badge className="absolute top-4 left-4 bg-accent text-accent-foreground">
                  Featured
                </Badge>
              )}
              <div className="absolute top-4 right-4 flex gap-2">
                <WishlistButton campsiteId={campsite.id} />
                <Button variant="secondary" size="sm">
                  <Share2 className="h-4 w-4" />
                </Button>
              </div>
            </div>
            
            {/* Additional Images */}
            <div className="grid grid-cols-3 gap-2 mt-4">
              {campsite.images.slice(1, 4).map((image, index) => (
                <div key={index} className="h-24 rounded overflow-hidden">
                  <img 
                    src={image} 
                    alt={`${campsite.name} ${index + 2}`}
                    className="w-full h-full object-cover"
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Booking Card */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <span className="text-2xl font-bold">R{campsite.price}</span>
                    <span className="text-muted-foreground">/night</span>
                  </div>
                  <div className="flex items-center">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                    <span className="font-medium">{campsite.rating}</span>
                    <span className="text-muted-foreground ml-1">
                      ({campsite.reviewCount} reviews)
                    </span>
                  </div>
                </div>

                {/* Promotions */}
                {promotions.length > 0 && (
                  <div className="mb-4">
                    {promotions.map(promotion => (
                      <div key={promotion.id} className="bg-green-50 border border-green-200 rounded-lg p-3 mb-2">
                        <div className="font-semibold text-green-800">{promotion.title}</div>
                        <div className="text-sm text-green-600">{promotion.description}</div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Dynamic Pricing */}
                <div className="mb-6">
                  <DynamicPricing campsite={campsite} />
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Check-in</label>
                      <Button variant="outline" className="w-full justify-start">
                        <Calendar className="h-4 w-4 mr-2" />
                        Add date
                      </Button>
                    </div>
                    <div>
                      <label className="text-sm font-medium">Check-out</label>
                      <Button variant="outline" className="w-full justify-start">
                        <Calendar className="h-4 w-4 mr-2" />
                        Add date
                      </Button>
                    </div>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium">Guests</label>
                    <Button variant="outline" className="w-full justify-start">
                      <Users className="h-4 w-4 mr-2" />
                      1 guest
                    </Button>
                  </div>

                  <Button className="w-full" size="lg">
                    Reserve
                  </Button>
                  
                  <p className="text-sm text-muted-foreground text-center">
                    You won't be charged yet
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Main Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-8">
            {/* Basic Info */}
            <div>
              <h1 className="text-3xl font-bold mb-2">{campsite.name}</h1>
              <div className="flex items-center text-muted-foreground mb-4">
                <MapPin className="h-4 w-4 mr-1" />
                <span>{campsite.location}, {campsite.province}</span>
              </div>
              
              <div className="flex items-center gap-4 mb-6">
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                  <span className="text-sm">Up to {campsite.capacity?.maxGuests || 4} guests</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1 text-muted-foreground" />
                  <span className="text-sm">Check-in: {campsite.checkInTime}</span>
                </div>
              </div>

              <p className="text-muted-foreground">{campsite.description}</p>
            </div>

            {/* Amenities */}
            <div>
              <h2 className="text-xl font-semibold mb-4">What this place offers</h2>
              <div className="grid grid-cols-2 gap-4">
                {campsite.amenities.map(amenity => (
                  <div key={amenity} className="flex items-center">
                    {getAmenityIcon(amenity)}
                    <span className="ml-3">{amenity}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Reviews Section */}
            <div>
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold">Reviews</h2>
                <Dialog open={showReviewDialog} onOpenChange={setShowReviewDialog}>
                  <DialogTrigger asChild>
                    <Button variant="outline">
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Write a review
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Write a review</DialogTitle>
                      <DialogDescription>
                        Share your experience at {campsite.name}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div>
                        <label className="text-sm font-medium mb-2 block">Rating</label>
                        {renderStars(newReview.rating, true, (rating) =>
                          setNewReview(prev => ({ ...prev, rating }))
                        )}
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Title</label>
                        <Input
                          placeholder="Summarize your experience"
                          value={newReview.title}
                          onChange={(e) => setNewReview(prev => ({ ...prev, title: e.target.value }))}
                        />
                      </div>
                      <div>
                        <label className="text-sm font-medium mb-2 block">Review</label>
                        <Textarea
                          placeholder="Tell others about your stay..."
                          value={newReview.comment}
                          onChange={(e) => setNewReview(prev => ({ ...prev, comment: e.target.value }))}
                          rows={4}
                        />
                      </div>
                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setShowReviewDialog(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleSubmitReview}>
                          Submit Review
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </div>

              {/* Review Summary */}
              <div className="mb-8">
                <ReviewSummary
                  campsiteId={campsite.id}
                  totalRating={campsite.rating}
                  totalReviews={campsite.reviewCount}
                />
              </div>

              {/* Reviews List */}
              <ReviewList reviews={reviews} maxReviews={5} />
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            {/* Host Info */}
            {provider && (
              <Card className="mb-6">
                <CardHeader>
                  <CardTitle className="text-lg">Hosted by {provider.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center gap-4 mb-4">
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={provider.avatar} />
                      <AvatarFallback>{provider.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{provider.name}</div>
                      <div className="text-sm text-muted-foreground">
                        Joined {new Date(provider.joinDate).getFullYear()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Response rate:</span>
                      <span>{provider.responseRate}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Response time:</span>
                      <span>{provider.responseTime}</span>
                    </div>
                  </div>
                  
                  <Button variant="outline" className="w-full mt-4">
                    Contact Host
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Policies */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Policies</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3 text-sm">
                <div>
                  <span className="font-medium">Check-in:</span> {campsite.checkInTime}
                </div>
                <div>
                  <span className="font-medium">Check-out:</span> {campsite.checkOutTime}
                </div>
                <div>
                  <span className="font-medium">Cancellation:</span> {campsite.policies.cancellation}
                </div>
                <div>
                  <span className="font-medium">Pets:</span> {campsite.policies.pets ? 'Allowed' : 'Not allowed'}
                </div>
                <div>
                  <span className="font-medium">Smoking:</span> {campsite.policies.smoking ? 'Allowed' : 'Not allowed'}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CampsiteDetail;
