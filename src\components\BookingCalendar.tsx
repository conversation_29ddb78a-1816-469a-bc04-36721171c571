import { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, LogIn, LogOut, Clock } from 'lucide-react';

interface BookingCalendarProps {
  campsiteId: string;
  price: number;
  onDateSelect?: (checkIn: Date, checkOut: Date) => void;
}

const BookingCalendar = ({ campsiteId, price, onDateSelect }: BookingCalendarProps) => {
  const [selectedCheckIn, setSelectedCheckIn] = useState<Date | null>(null);
  const [selectedCheckOut, setSelectedCheckOut] = useState<Date | null>(null);
  const [currentMonth, setCurrentMonth] = useState(new Date());

  // Mock availability data - in real app would come from API
  const availability = {
    available: ['2024-12-01', '2024-12-02', '2024-12-03', '2024-12-05', '2024-12-06'],
    unavailable: ['2024-12-04', '2024-12-07', '2024-12-08'],
    discounted: ['2024-12-09', '2024-12-10', '2024-12-11']
  };

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    const startingDayOfWeek = firstDay.getDay();
    
    const days = [];
    
    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null);
    }
    
    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day));
    }
    
    return days;
  };

  const formatDateKey = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  const getDateStatus = (date: Date) => {
    const dateKey = formatDateKey(date);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    if (date < today) return 'past';
    if (availability.unavailable.includes(dateKey)) return 'unavailable';
    if (availability.discounted.includes(dateKey)) return 'discounted';
    if (availability.available.includes(dateKey)) return 'available';
    return 'available'; // Default to available
  };

  const handleDateClick = (date: Date) => {
    const status = getDateStatus(date);
    if (status === 'past' || status === 'unavailable') return;

    if (!selectedCheckIn || (selectedCheckIn && selectedCheckOut)) {
      // First selection or reset
      setSelectedCheckIn(date);
      setSelectedCheckOut(null);
    } else if (date > selectedCheckIn) {
      // Second selection (checkout)
      setSelectedCheckOut(date);
      onDateSelect?.(selectedCheckIn, date);
    } else {
      // New check-in before current check-in
      setSelectedCheckIn(date);
      setSelectedCheckOut(null);
    }
  };

  const isDateInRange = (date: Date) => {
    if (!selectedCheckIn || !selectedCheckOut) return false;
    return date >= selectedCheckIn && date <= selectedCheckOut;
  };

  const getDayClassName = (date: Date | null) => {
    if (!date) return 'invisible';
    
    const status = getDateStatus(date);
    const isSelected = selectedCheckIn && formatDateKey(date) === formatDateKey(selectedCheckIn);
    const isCheckOut = selectedCheckOut && formatDateKey(date) === formatDateKey(selectedCheckOut);
    const isInRange = isDateInRange(date);
    
    let className = 'w-10 h-10 rounded-lg flex items-center justify-center text-sm cursor-pointer transition-colors ';
    
    if (status === 'past' || status === 'unavailable') {
      className += 'text-muted-foreground bg-muted cursor-not-allowed line-through';
    } else if (isSelected || isCheckOut) {
      className += 'bg-primary text-primary-foreground font-semibold';
    } else if (isInRange) {
      className += 'bg-primary/20 text-primary';
    } else if (status === 'discounted') {
      className += 'bg-green-100 text-green-800 hover:bg-green-200';
    } else {
      className += 'hover:bg-muted';
    }
    
    return className;
  };

  const getPriceForDate = (date: Date) => {
    const status = getDateStatus(date);
    if (status === 'discounted') return Math.round(price * 0.8);
    return price;
  };

  const calculateTotalPrice = () => {
    if (!selectedCheckIn || !selectedCheckOut) return 0;
    
    let total = 0;
    const current = new Date(selectedCheckIn);
    
    while (current < selectedCheckOut) {
      total += getPriceForDate(current);
      current.setDate(current.getDate() + 1);
    }
    
    return total;
  };

  const getNights = () => {
    if (!selectedCheckIn || !selectedCheckOut) return 0;
    const diffTime = selectedCheckOut.getTime() - selectedCheckIn.getTime();
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  const days = getDaysInMonth(currentMonth);
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const previousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  const nextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Select Dates
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Calendar Navigation */}
        <div className="flex items-center justify-between">
          <Button variant="outline" size="sm" onClick={previousMonth}>
            ←
          </Button>
          <h3 className="font-semibold">
            {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
          </h3>
          <Button variant="outline" size="sm" onClick={nextMonth}>
            →
          </Button>
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7 gap-1">
          {/* Days of week header */}
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
            <div key={day} className="text-center text-sm font-medium text-muted-foreground py-2">
              {day}
            </div>
          ))}
          
          {/* Calendar days */}
          {days.map((date, index) => (
            <div
              key={index}
              className={getDayClassName(date)}
              onClick={() => date && handleDateClick(date)}
            >
              {date && (
                <div className="text-center">
                  <div>{date.getDate()}</div>
                  {date && getDateStatus(date) === 'discounted' && (
                    <div className="text-xs font-bold text-green-600">
                      R{getPriceForDate(date)}
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Legend */}
        <div className="space-y-2 text-xs">
          <div className="font-medium">Legend:</div>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-primary rounded"></div>
              <span>Selected</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-green-100 rounded"></div>
              <span>Discounted</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-muted rounded"></div>
              <span>Unavailable</span>
            </div>
          </div>
        </div>

        {/* Selected Dates Summary */}
        {selectedCheckIn && (
          <div className="border-t pt-4 space-y-3">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="flex items-center gap-2 text-sm font-medium">
                  <LogIn className="h-4 w-4" />
                  Check-in
                </div>
                <div className="text-sm text-muted-foreground">
                  {selectedCheckIn.toLocaleDateString('en-ZA', { 
                    weekday: 'short', 
                    year: 'numeric', 
                    month: 'short', 
                    day: 'numeric' 
                  })}
                </div>
              </div>
              
              {selectedCheckOut && (
                <div>
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <LogOut className="h-4 w-4" />
                    Check-out
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {selectedCheckOut.toLocaleDateString('en-ZA', { 
                      weekday: 'short', 
                      year: 'numeric', 
                      month: 'short', 
                      day: 'numeric' 
                    })}
                  </div>
                </div>
              )}
            </div>

            {selectedCheckOut && (
              <div className="border-t pt-3">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    <span className="text-sm">{getNights()} night{getNights() !== 1 ? 's' : ''}</span>
                  </div>
                  <div className="text-lg font-semibold">
                    R{calculateTotalPrice().toLocaleString()}
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BookingCalendar;