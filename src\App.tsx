import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { WishlistProvider } from "@/contexts/WishlistContext";
import Index from "./pages/Index";
import Search from "./pages/Search";
import CampsiteDetail from "./pages/CampsiteDetail";
import Profile from "./pages/Profile";
import Community from "./pages/Community";
import BlogPost from "./pages/BlogPost";
import ProviderDashboard from "./pages/ProviderDashboard";
import PromotionsManager from "./pages/PromotionsManager";
import Messages from "./pages/Messages";
import Subscription from "./pages/Subscription";
import Deals from "./pages/Deals";
import Events from "./pages/Events";
import TripPlanner from "./pages/TripPlanner";
import Favorites from "./pages/Favorites";
import Membership from "./pages/Membership";
import MapView from "./pages/MapView";
import Booking from "./pages/Booking";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <WishlistProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
        <Routes>
          <Route path="/" element={<Index />} />
          <Route path="/search" element={<Search />} />
          <Route path="/campsite/:id" element={<CampsiteDetail />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/community" element={<Community />} />
          <Route path="/blog/:slug" element={<BlogPost />} />
          <Route path="/provider" element={<ProviderDashboard />} />
          <Route path="/provider/promotions" element={<PromotionsManager />} />
          <Route path="/messages" element={<Messages />} />
          <Route path="/subscription" element={<Subscription />} />
          <Route path="/deals" element={<Deals />} />
          <Route path="/events" element={<Events />} />
          <Route path="/plan-trip" element={<TripPlanner />} />
          <Route path="/favorites" element={<Favorites />} />
          <Route path="/membership" element={<Membership />} />
          <Route path="/map" element={<MapView />} />
          <Route path="/booking/:id" element={<Booking />} />
          {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
          <Route path="*" element={<NotFound />} />
        </Routes>
        </BrowserRouter>
      </WishlistProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
