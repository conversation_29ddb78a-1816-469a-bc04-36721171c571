import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MapPin, 
  Calendar, 
  Star, 
  Phone, 
  Mail, 
  Globe, 
  Tag,
  Clock,
  Users,
  Filter,
  Search
} from 'lucide-react';
import { mockDeals, getFeaturedDeals, getDealsByType } from '@/data/mock-deals';

const Deals = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('featured');

  const provinces = [
    'Western Cape', 'KwaZulu-Natal', 'Gauteng', 'Mpumalanga', 
    'Limpopo', 'Eastern Cape', 'Free State', 'North West', 'Northern Cape'
  ];

  const partnerTypes = [
    { value: 'campsite', label: 'Campsites' },
    { value: 'restaurant', label: 'Restaurants' },
    { value: 'outdoor_gear', label: 'Outdoor Gear' },
    { value: 'tour_guide', label: 'Tour Guides' },
    { value: 'attraction', label: 'Attractions' },
    { value: 'fuel_station', label: 'Fuel Stations' }
  ];

  // Filter and sort deals
  const filteredDeals = mockDeals
    .filter(deal => {
      const matchesSearch = deal.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           deal.partnerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           deal.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesProvince = selectedProvince === 'all' || deal.province === selectedProvince;
      const matchesType = selectedType === 'all' || deal.partnerType === selectedType;
      const isActive = new Date(deal.expiryDate) > new Date();
      
      return matchesSearch && matchesProvince && matchesType && isActive;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'featured':
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
        case 'expiry':
          return new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime();
        case 'discount':
          return b.discountValue - a.discountValue;
        case 'rating':
          return b.partnerRating - a.partnerRating;
        default:
          return 0;
      }
    });

  const getDiscountText = (deal: any) => {
    switch (deal.discountType) {
      case 'percentage':
        return `${deal.discountValue}% OFF`;
      case 'fixed_amount':
        return `R${deal.discountValue} OFF`;
      case 'buy_one_get_one':
        return 'BUY 1 GET 1 FREE';
      case 'free_item':
        return 'FREE ITEM';
      default:
        return 'SPECIAL OFFER';
    }
  };

  const getPartnerTypeIcon = (type: string) => {
    switch (type) {
      case 'campsite': return '🏕️';
      case 'restaurant': return '🍽️';
      case 'outdoor_gear': return '🎒';
      case 'tour_guide': return '🥾';
      case 'attraction': return '🎯';
      case 'fuel_station': return '⛽';
      default: return '🏪';
    }
  };

  const getDaysUntilExpiry = (expiryDate: string) => {
    const today = new Date();
    const expiry = new Date(expiryDate);
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const featuredDeals = getFeaturedDeals();

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Deals for Campers</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover exclusive discounts and special offers from our trusted partners across South Africa
          </p>
        </div>

        {/* Featured Deals */}
        {featuredDeals.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">🔥 Featured Deals</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredDeals.slice(0, 3).map(deal => (
                <Card key={deal.id} className="relative overflow-hidden border-2 border-primary/20">
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-red-500 text-white font-bold">
                      {getDiscountText(deal)}
                    </Badge>
                  </div>
                  
                  <div className="relative h-48 bg-muted">
                    <img 
                      src={deal.image} 
                      alt={deal.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-white/90">
                        <span className="mr-1">{getPartnerTypeIcon(deal.partnerType)}</span>
                        {partnerTypes.find(t => t.value === deal.partnerType)?.label}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{deal.partnerRating}</span>
                      <span className="text-sm text-muted-foreground">• {deal.partnerName}</span>
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-2">{deal.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                      {deal.description}
                    </p>
                    
                    <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-1">
                        <MapPin className="h-4 w-4" />
                        <span>{deal.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        <span>{getDaysUntilExpiry(deal.expiryDate)} days left</span>
                      </div>
                    </div>
                    
                    <Button className="w-full">
                      View Deal Details
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-card rounded-lg p-6 mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Filter className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Filter Deals</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search deals..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedProvince} onValueChange={setSelectedProvince}>
              <SelectTrigger>
                <SelectValue placeholder="All Provinces" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Provinces</SelectItem>
                {provinces.map(province => (
                  <SelectItem key={province} value={province}>{province}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {partnerTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="featured">Featured First</SelectItem>
                <SelectItem value="expiry">Expiring Soon</SelectItem>
                <SelectItem value="discount">Best Discount</SelectItem>
                <SelectItem value="rating">Highest Rated</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* All Deals */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">All Deals ({filteredDeals.length})</h2>
          </div>
          
          {filteredDeals.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="text-6xl mb-4">🔍</div>
              <h3 className="text-xl font-semibold mb-2">No deals found</h3>
              <p className="text-muted-foreground">
                Try adjusting your filters or search terms to find more deals.
              </p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredDeals.map(deal => (
                <Card key={deal.id} className="relative overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="absolute top-4 right-4 z-10">
                    <Badge variant={deal.featured ? 'default' : 'secondary'}>
                      {getDiscountText(deal)}
                    </Badge>
                  </div>
                  
                  <div className="relative h-48 bg-muted">
                    <img 
                      src={deal.image} 
                      alt={deal.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-white/90">
                        <span className="mr-1">{getPartnerTypeIcon(deal.partnerType)}</span>
                        {partnerTypes.find(t => t.value === deal.partnerType)?.label}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="text-sm font-medium">{deal.partnerRating}</span>
                      <span className="text-sm text-muted-foreground">• {deal.partnerName}</span>
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-2">{deal.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                      {deal.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{deal.location}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>Expires: {new Date(deal.expiryDate).toLocaleDateString()}</span>
                      </div>
                      {deal.maxRedemptions && (
                        <div className="flex items-center gap-1 text-sm text-muted-foreground">
                          <Users className="h-4 w-4" />
                          <span>{deal.maxRedemptions - deal.currentRedemptions} left</span>
                        </div>
                      )}
                    </div>
                    
                    <Button className="w-full" variant="outline">
                      View Deal Details
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Deals;
