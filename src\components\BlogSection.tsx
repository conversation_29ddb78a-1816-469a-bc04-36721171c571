import { <PERSON>, CardContent } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar, Clock, ArrowRight, TrendingUp, Star } from 'lucide-react';
import { mockBlogPosts } from '@/data/mock-blog-posts';

const BlogSection = () => {
  const featuredPost = mockBlogPosts[0];
  const recentPosts = mockBlogPosts.slice(1, 4);
  const trendingPosts = mockBlogPosts.slice(4, 6);

  return (
    <div className="space-y-8">
      {/* Section Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold mb-4">Camping Stories & Tips</h2>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Discover hidden gems, expert camping tips, and inspiring stories from fellow adventurers across South Africa
        </p>
      </div>

      {/* Featured Article */}
      <Card className="overflow-hidden">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
          <div className="relative h-64 lg:h-auto">
            <img 
              src={featuredPost.image} 
              alt={featuredPost.title}
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 left-4">
              <Badge className="bg-accent text-accent-foreground">Featured</Badge>
            </div>
          </div>
          
          <CardContent className="p-6 lg:p-8 flex flex-col justify-center">
            <div className="space-y-4">
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {new Date(featuredPost.publishDate).toLocaleDateString('en-ZA', { 
                    year: 'numeric', 
                    month: 'long', 
                    day: 'numeric' 
                  })}
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {featuredPost.readTime}
                </div>
              </div>
              
              <h3 className="text-2xl font-bold leading-tight">{featuredPost.title}</h3>
              
              <p className="text-muted-foreground">{featuredPost.excerpt}</p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="w-8 h-8">
                    <AvatarImage src={featuredPost.authorAvatar} />
                    <AvatarFallback>{featuredPost.author.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span className="text-sm font-medium">{featuredPost.author}</span>
                </div>
                
                <Button asChild>
                  <a href={`/blog/${featuredPost.slug}`}>
                    Read More
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </a>
                </Button>
              </div>
            </div>
          </CardContent>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Recent Posts */}
        <div className="lg:col-span-2 space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold">Latest Stories</h3>
            <Button variant="outline" size="sm" asChild>
              <a href="/blog">View All</a>
            </Button>
          </div>
          
          <div className="space-y-6">
            {recentPosts.map(post => (
              <Card key={post.id} className="overflow-hidden hover:shadow-md transition-shadow">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-0">
                  <div className="relative h-32 md:h-auto">
                    <img 
                      src={post.image} 
                      alt={post.title}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  
                  <CardContent className="md:col-span-2 p-4">
                    <div className="space-y-2">
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <span>{post.category}</span>
                        <span>•</span>
                        <span>{new Date(post.publishDate).toLocaleDateString('en-ZA')}</span>
                        <span>•</span>
                        <span>{post.readTime}</span>
                      </div>
                      
                      <h4 className="font-semibold leading-tight line-clamp-2">{post.title}</h4>
                      
                      <p className="text-sm text-muted-foreground line-clamp-2">{post.excerpt}</p>
                      
                      <div className="flex items-center justify-between pt-2">
                        <div className="flex items-center gap-2">
                          <Avatar className="w-6 h-6">
                            <AvatarImage src={post.authorAvatar} />
                            <AvatarFallback className="text-xs">{post.author.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <span className="text-xs text-muted-foreground">{post.author}</span>
                        </div>
                        
                        <Button variant="ghost" size="sm" asChild>
                          <a href={`/blog/${post.slug}`}>Read</a>
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </div>
              </Card>
            ))}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Trending Posts */}
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-2 mb-4">
                <TrendingUp className="h-5 w-5 text-primary" />
                <h3 className="font-semibold">Trending This Week</h3>
              </div>
              
              <div className="space-y-4">
                {trendingPosts.map((post, index) => (
                  <div key={post.id} className="space-y-2">
                    <div className="flex items-start gap-3">
                      <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-xs font-bold">
                        {index + 1}
                      </div>
                      <div className="flex-1 min-w-0">
                        <h4 className="text-sm font-medium leading-tight line-clamp-2">
                          <a href={`/blog/${post.slug}`} className="hover:text-primary">
                            {post.title}
                          </a>
                        </h4>
                        <div className="flex items-center gap-1 mt-1 text-xs text-muted-foreground">
                          <Calendar className="h-3 w-3" />
                          {new Date(post.publishDate).toLocaleDateString('en-ZA')}
                        </div>
                      </div>
                    </div>
                    {index < trendingPosts.length - 1 && <div className="border-b" />}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Categories */}
          <Card>
            <CardContent className="p-6">
              <h3 className="font-semibold mb-4">Popular Categories</h3>
              
              <div className="space-y-2">
                {[
                  { name: 'Camping Tips', count: 24 },
                  { name: 'Hidden Gems', count: 18 },
                  { name: 'Gear Reviews', count: 15 },
                  { name: 'Family Camping', count: 12 },
                  { name: 'Adventure Stories', count: 10 },
                  { name: 'Wildlife Encounters', count: 8 }
                ].map(category => (
                  <div key={category.name} className="flex items-center justify-between text-sm">
                    <a href={`/blog/category/${category.name.toLowerCase().replace(' ', '-')}`} 
                       className="hover:text-primary cursor-pointer">
                      {category.name}
                    </a>
                    <span className="text-muted-foreground">({category.count})</span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Newsletter Signup */}
          <Card className="bg-primary text-primary-foreground">
            <CardContent className="p-6 text-center">
              <Star className="h-8 w-8 mx-auto mb-3" />
              <h3 className="font-semibold mb-2">Weekly Adventure Digest</h3>
              <p className="text-sm mb-4 text-primary-foreground/80">
                Get the latest camping tips, hidden gems, and exclusive deals delivered to your inbox
              </p>
              <Button variant="secondary" size="sm" className="w-full">
                Subscribe Now
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default BlogSection;