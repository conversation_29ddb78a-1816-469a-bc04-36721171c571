import React, { createContext, useContext, useState, useEffect } from 'react';

interface WishlistContextType {
  favorites: string[];
  addToFavorites: (campsiteId: string) => void;
  removeFromFavorites: (campsiteId: string) => void;
  isFavorite: (campsiteId: string) => boolean;
  toggleFavorite: (campsiteId: string) => void;
}

const WishlistContext = createContext<WishlistContextType | undefined>(undefined);

export function WishlistProvider({ children }: { children: React.ReactNode }) {
  const [favorites, setFavorites] = useState<string[]>([]);

  // Load favorites from localStorage on mount
  useEffect(() => {
    const savedFavorites = localStorage.getItem('campspot-favorites');
    if (savedFavorites) {
      try {
        setFavorites(JSON.parse(savedFavorites));
      } catch (error) {
        console.error('Error loading favorites from localStorage:', error);
      }
    }
  }, []);

  // Save favorites to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('campspot-favorites', JSON.stringify(favorites));
  }, [favorites]);

  const addToFavorites = (campsiteId: string) => {
    setFavorites(prev => {
      if (!prev.includes(campsiteId)) {
        return [...prev, campsiteId];
      }
      return prev;
    });
  };

  const removeFromFavorites = (campsiteId: string) => {
    setFavorites(prev => prev.filter(id => id !== campsiteId));
  };

  const isFavorite = (campsiteId: string) => {
    return favorites.includes(campsiteId);
  };

  const toggleFavorite = (campsiteId: string) => {
    if (isFavorite(campsiteId)) {
      removeFromFavorites(campsiteId);
    } else {
      addToFavorites(campsiteId);
    }
  };

  const value = {
    favorites,
    addToFavorites,
    removeFromFavorites,
    isFavorite,
    toggleFavorite
  };

  return (
    <WishlistContext.Provider value={value}>
      {children}
    </WishlistContext.Provider>
  );
}

export function useWishlist() {
  const context = useContext(WishlistContext);
  if (context === undefined) {
    throw new Error('useWishlist must be used within a WishlistProvider');
  }
  return context;
}

// Hook for wishlist button component
export function useWishlistButton(campsiteId: string) {
  const { isFavorite, toggleFavorite } = useWishlist();
  
  return {
    isFavorite: isFavorite(campsiteId),
    toggleFavorite: () => toggleFavorite(campsiteId)
  };
}
