import { useState, useMemo } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { WishlistButton } from '@/components/ui/wishlist-button';
import { CampsiteBadges } from '@/components/ui/badge-display';
import { 
  Heart, 
  MapPin, 
  Star, 
  DollarSign,
  Grid3X3,
  List,
  Map,
  Filter,
  Search,
  Calendar,
  Users,
  Tent
} from 'lucide-react';
import { useWishlist } from '@/contexts/WishlistContext';
import { allCampsites } from '@/data';

const Favorites = () => {
  const { favorites } = useWishlist();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [sortBy, setSortBy] = useState('name');
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'map'>('grid');

  const provinces = [
    'Western Cape', 'KwaZulu-Natal', 'Gauteng', 'Mpumalanga', 
    'Limpopo', 'Eastern Cape', 'Free State', 'North West', 'Northern Cape'
  ];

  const types = [
    { value: 'tent', label: 'Tent Sites' },
    { value: 'caravan', label: 'Caravan Parks' },
    { value: 'glamping', label: 'Glamping' },
    { value: 'chalet', label: 'Chalets' }
  ];

  // Get favorite campsites
  const favoriteCampsites = useMemo(() => {
    return allCampsites.filter(campsite => favorites.includes(campsite.id));
  }, [favorites]);

  // Filter and sort favorite campsites
  const filteredFavorites = useMemo(() => {
    return favoriteCampsites
      .filter(campsite => {
        const matchesSearch = campsite.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             campsite.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
                             campsite.description.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesProvince = selectedProvince === 'all' || campsite.province === selectedProvince;
        const matchesType = selectedType === 'all' || campsite.type === selectedType;
        
        return matchesSearch && matchesProvince && matchesType;
      })
      .sort((a, b) => {
        switch (sortBy) {
          case 'name':
            return a.name.localeCompare(b.name);
          case 'price':
            return a.price - b.price;
          case 'rating':
            return b.rating - a.rating;
          case 'location':
            return a.location.localeCompare(b.location);
          default:
            return 0;
        }
      });
  }, [favoriteCampsites, searchTerm, selectedProvince, selectedType, sortBy]);

  // Group favorites by province for statistics
  const favoritesByProvince = useMemo(() => {
    return favoriteCampsites.reduce((acc, campsite) => {
      acc[campsite.province] = (acc[campsite.province] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
  }, [favoriteCampsites]);

  const averagePrice = useMemo(() => {
    if (favoriteCampsites.length === 0) return 0;
    return Math.round(favoriteCampsites.reduce((sum, campsite) => sum + campsite.price, 0) / favoriteCampsites.length);
  }, [favoriteCampsites]);

  const averageRating = useMemo(() => {
    if (favoriteCampsites.length === 0) return 0;
    return (favoriteCampsites.reduce((sum, campsite) => sum + campsite.rating, 0) / favoriteCampsites.length).toFixed(1);
  }, [favoriteCampsites]);

  const CampsiteCard = ({ campsite, isListView = false }: { campsite: any; isListView?: boolean }) => {
    if (isListView) {
      return (
        <Card className="overflow-hidden hover:shadow-lg transition-shadow">
          <div className="md:flex">
            <div className="md:w-1/4">
              <div className="relative h-48 md:h-full bg-muted">
                <img 
                  src={campsite.image} 
                  alt={campsite.name}
                  className="w-full h-full object-cover"
                />
                <div className="absolute top-4 right-4">
                  <WishlistButton campsiteId={campsite.id} />
                </div>
              </div>
            </div>
            
            <div className="md:w-3/4 p-6">
              <div className="flex items-start justify-between mb-2">
                <div>
                  <h3 className="text-xl font-semibold mb-1">{campsite.name}</h3>
                  <p className="text-muted-foreground flex items-center">
                    <MapPin className="h-4 w-4 mr-1" />
                    {campsite.location}, {campsite.province}
                  </p>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">R{campsite.price}</div>
                  <div className="text-sm text-muted-foreground">per night</div>
                </div>
              </div>
              
              <p className="text-muted-foreground mb-4 line-clamp-2">
                {campsite.description}
              </p>
              
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-4">
                  <div className="flex items-center">
                    <Star className="h-4 w-4 text-yellow-500 fill-current mr-1" />
                    <span className="font-medium">{campsite.rating}</span>
                    <span className="text-muted-foreground ml-1">({campsite.reviewCount} reviews)</span>
                  </div>
                  <Badge variant="outline" className="capitalize">
                    <Tent className="h-3 w-3 mr-1" />
                    {campsite.type}
                  </Badge>
                </div>
              </div>
              
              <div className="mb-4">
                <CampsiteBadges 
                  badgeIds={campsite.badges || []} 
                  featured={campsite.featured}
                  compact={false}
                />
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center">
                    <Users className="h-4 w-4 mr-1" />
                    <span>Up to {campsite.capacity?.maxGuests || 4} guests</span>
                  </div>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    <span>Check-in: {campsite.checkInTime || '14:00'}</span>
                  </div>
                </div>
                
                <Button>
                  View Details
                </Button>
              </div>
            </div>
          </div>
        </Card>
      );
    }

    return (
      <Card className="overflow-hidden hover:shadow-lg transition-shadow">
        <div className="relative h-48 bg-muted">
          <img 
            src={campsite.image} 
            alt={campsite.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute top-4 right-4 flex gap-2">
            <WishlistButton campsiteId={campsite.id} />
            <div className="bg-card/90 backdrop-blur-sm rounded-full px-3 py-1">
              <span className="text-sm font-semibold text-foreground">
                R{campsite.price}/night
              </span>
            </div>
          </div>
          {campsite.featured && (
            <div className="absolute top-4 left-4">
              <Badge className="bg-primary text-primary-foreground">
                ⭐ Featured
              </Badge>
            </div>
          )}
        </div>
        
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-2">
            <h3 className="text-lg font-semibold">{campsite.name}</h3>
            <div className="flex items-center">
              <Star className="h-4 w-4 text-yellow-500 fill-current" />
              <span className="text-sm text-muted-foreground ml-1">
                {campsite.rating}
              </span>
            </div>
          </div>
          
          <p className="text-muted-foreground text-sm mb-4 flex items-center">
            <MapPin className="h-4 w-4 mr-1" />
            {campsite.location}, {campsite.province}
          </p>
          
          <div className="mb-4">
            <CampsiteBadges 
              badgeIds={campsite.badges || []} 
              featured={false}
              compact={true}
            />
          </div>
          
          <Button className="w-full" variant="outline">
            View Details
          </Button>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4 flex items-center justify-center gap-2">
            <Heart className="h-8 w-8 text-red-500 fill-current" />
            My Saved Campsites
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Your personal collection of favorite camping destinations across South Africa
          </p>
        </div>

        {favorites.length === 0 ? (
          <Card className="p-12 text-center max-w-2xl mx-auto">
            <div className="text-6xl mb-4">💔</div>
            <h3 className="text-xl font-semibold mb-2">No saved campsites yet</h3>
            <p className="text-muted-foreground mb-6">
              Start exploring and save your favorite campsites to see them here. 
              Click the heart icon on any campsite to add it to your favorites.
            </p>
            <Button asChild>
              <a href="/search">Discover Campsites</a>
            </Button>
          </Card>
        ) : (
          <>
            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-primary">{favorites.length}</div>
                  <div className="text-sm text-muted-foreground">Saved Campsites</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-green-600">R{averagePrice}</div>
                  <div className="text-sm text-muted-foreground">Average Price</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-blue-600">{averageRating}</div>
                  <div className="text-sm text-muted-foreground">Average Rating</div>
                </CardContent>
              </Card>
              <Card className="text-center">
                <CardContent className="p-4">
                  <div className="text-2xl font-bold text-purple-600">{Object.keys(favoritesByProvince).length}</div>
                  <div className="text-sm text-muted-foreground">Provinces</div>
                </CardContent>
              </Card>
            </div>

            {/* Filters and View Controls */}
            <div className="bg-card rounded-lg p-6 mb-8">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  <h3 className="text-lg font-semibold">Filter & Sort</h3>
                </div>
                
                <div className="flex items-center gap-2">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                  >
                    <List className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'map' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setViewMode('map')}
                    disabled
                    title="Map view coming soon"
                  >
                    <Map className="h-4 w-4" />
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search saved campsites..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
                
                <Select value={selectedProvince} onValueChange={setSelectedProvince}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Provinces" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Provinces</SelectItem>
                    {provinces.map(province => (
                      <SelectItem key={province} value={province}>{province}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={selectedType} onValueChange={setSelectedType}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    {types.map(type => (
                      <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="name">Name (A-Z)</SelectItem>
                    <SelectItem value="price">Price (Low to High)</SelectItem>
                    <SelectItem value="rating">Rating (High to Low)</SelectItem>
                    <SelectItem value="location">Location (A-Z)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Results */}
            <div className="mb-8">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-2xl font-bold">
                  {filteredFavorites.length === favorites.length 
                    ? `All Saved Campsites (${favorites.length})`
                    : `Filtered Results (${filteredFavorites.length} of ${favorites.length})`
                  }
                </h2>
              </div>
              
              {filteredFavorites.length === 0 ? (
                <Card className="p-12 text-center">
                  <div className="text-6xl mb-4">🔍</div>
                  <h3 className="text-xl font-semibold mb-2">No campsites match your filters</h3>
                  <p className="text-muted-foreground">
                    Try adjusting your search criteria to find your saved campsites.
                  </p>
                </Card>
              ) : (
                <div className={
                  viewMode === 'grid' 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
                    : "space-y-6"
                }>
                  {filteredFavorites.map(campsite => (
                    <CampsiteCard 
                      key={campsite.id} 
                      campsite={campsite} 
                      isListView={viewMode === 'list'}
                    />
                  ))}
                </div>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default Favorites;
