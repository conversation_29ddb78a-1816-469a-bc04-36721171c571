// Export all data models and mock data
export * from './mock-campsites';
export * from './mock-users';
export * from './mock-providers';
export * from './mock-reviews';
export * from './mock-blog-posts';
export * from './mock-forum-posts';
export * from './mock-promotions';
export * from './mock-deals';
export * from './mock-events';
export * from './mock-itineraries';
export * from './badges';


// Combined exports for convenience
import { allCampsites as mockCampsites } from './mock-campsites';
import { mockProviders } from './mock-providers';
import { mockUsers } from './mock-users';
import { mockReviews } from './mock-reviews';
import { mockBlogPosts } from './mock-blog-posts';
import { mockForumPosts } from './mock-forum-posts';
import { mockPromotions, mockAnalytics } from './mock-promotions';
import { mockDeals } from './mock-deals';
import { mockEvents } from './mock-events';
import { mockItineraries } from './mock-itineraries';

// All campsites are now in the main mockCampsites array
export const allCampsites = mockCampsites;

// All providers
export const allProviders = mockProviders;

// Export all mock data collections
export const mockData = {
  campsites: allCampsites,
  providers: allProviders,
  users: mockUsers,
  reviews: mockReviews,
  blogPosts: mockBlogPosts,
  forumPosts: mockForumPosts,
  promotions: mockPromotions,
  analytics: mockAnalytics,
  deals: mockDeals,
  events: mockEvents,
  itineraries: mockItineraries
};

// Utility functions for data access
export const getCampsiteById = (id: string) => 
  allCampsites.find(campsite => campsite.id === id);

export const getProviderById = (id: string) => 
  allProviders.find(provider => provider.id === id);

export const getUserById = (id: string) => 
  mockUsers.find(user => user.id === id);

export const getReviewsByCampsiteId = (campsiteId: string) => 
  mockReviews.filter(review => review.campsiteId === campsiteId);

export const getPromotionsByCampsiteId = (campsiteId: string) => 
  mockPromotions.filter(promotion => promotion.campsiteId === campsiteId && promotion.active);

export const getAnalyticsByCampsiteId = (campsiteId: string) => 
  mockAnalytics.find(analytics => analytics.campsiteId === campsiteId);

export const getCampsitesByProvince = (province: string) => 
  allCampsites.filter(campsite => campsite.province === province);

export const getFeaturedCampsites = () => 
  allCampsites.filter(campsite => campsite.featured);

export const getCampsitesByType = (type: string) => 
  allCampsites.filter(campsite => campsite.type === type);

// Search and filter utilities
export const searchCampsites = (query: string) => 
  allCampsites.filter(campsite => 
    campsite.name.toLowerCase().includes(query.toLowerCase()) ||
    campsite.location.toLowerCase().includes(query.toLowerCase()) ||
    campsite.description.toLowerCase().includes(query.toLowerCase())
  );

export const filterCampsitesByAmenities = (amenities: string[]) => 
  allCampsites.filter(campsite => 
    amenities.every(amenity => campsite.amenities.includes(amenity))
  );

export const filterCampsitesByPriceRange = (minPrice: number, maxPrice: number) => 
  allCampsites.filter(campsite => 
    campsite.price >= minPrice && campsite.price <= maxPrice
  );

export const sortCampsites = (campsites: typeof allCampsites, sortBy: 'price-low' | 'price-high' | 'rating' | 'popular') => {
  switch (sortBy) {
    case 'price-low':
      return [...campsites].sort((a, b) => a.price - b.price);
    case 'price-high':
      return [...campsites].sort((a, b) => b.price - a.price);
    case 'rating':
      return [...campsites].sort((a, b) => b.rating - a.rating);
    case 'popular':
      return [...campsites].sort((a, b) => b.reviewCount - a.reviewCount);
    default:
      return campsites;
  }
};

// Province list for South Africa
export const SA_PROVINCES = [
  'Gauteng',
  'KwaZulu-Natal',
  'Western Cape',
  'Eastern Cape',
  'Mpumalanga',
  'Limpopo',
  'Free State',
  'North West',
  'Northern Cape'
];

// Campsite types
export const CAMPSITE_TYPES = [
  { value: 'tent', label: 'Tent Camping' },
  { value: 'caravan', label: 'Caravan/RV' },
  { value: 'glamping', label: 'Glamping' },
  { value: 'chalet', label: 'Chalet/Cabin' }
];

// Common amenities for filtering
export const COMMON_AMENITIES = [
  'Swimming Pool',
  'Private Ablutions',
  'Shared Ablutions',
  'Braai Facilities',
  'Hiking Trails',
  'Wi-Fi',
  'Electricity',
  'Restaurant',
  'Game Drives',
  'Beach Access',
  'Pet-Friendly',
  'Playground',
  'Fishing Access'
];
