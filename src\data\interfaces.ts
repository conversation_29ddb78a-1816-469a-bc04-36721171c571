// Add Analytics interface and fix imports
export interface Analytics {
  id?: string;
  campsiteId: string;
  totalViews?: number;
  totalBookings?: number;
  averageStayDuration?: number;
  revenue?: number;
  popularMonths?: string[];
  conversionRate?: number;
  repeatCustomers?: number;
  month?: string;
  occupancyRate?: number;
  bookings?: number;
  averageStay?: number;
  topAmenities?: string[];
  seasonalTrends?: any;
}

// Add Itinerary interface with packingList
export interface Itinerary {
  id: string;
  title: string;
  description: string;
  duration: string;
  difficulty: 'easy' | 'moderate' | 'challenging';
  distance: string;
  province: string;
  highlights: string[];
  route: Array<{
    day: number;
    location: string;
    distance: string;
    description: string;
    activities: string[];
    accommodation: string;
    type: 'campsite' | 'town' | 'attraction';
    highlights: string[];
    image: string;
  }>;
  recommendedCampsites: string[];
  packingList?: string[];
  featured: boolean;
  tags: string[];
  roadConditions: string;
  fuelStops: string[];
  image: string;
  createdBy: string;
  createdDate: string;
  rating: number;
  reviewCount: number;
  estimatedCost: {
    budget: number;
    midRange: number;
    luxury: number;
  };
  bestTime: string;
  requirements: string[];
}