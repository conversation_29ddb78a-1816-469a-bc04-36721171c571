// Enhanced interfaces for Phase 2
import { Analytics, Itinerary } from './interfaces';
export interface Review {
  id: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  campsiteId: string;
  rating: number;
  title: string;
  comment: string;
  date: string;
  verified: boolean;
  helpful: number;
  images?: string[];
}

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  joinDate: string;
  verified: boolean;
  reviewCount: number;
  favorites: string[];
  subscriptionTier: 'free' | 'premium';
  badges: string[];
}

export interface Provider {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  joinDate: string;
  subscriptionTier: 'free' | 'premium';
  campsiteIds: string[];
  totalBookings: number;
  averageRating: number;
  responseRate: number;
  responseTime: string;
}

export interface Promotion {
  id: string;
  campsiteId: string;
  title: string;
  description: string;
  discountType: 'percentage' | 'fixed';
  discountValue: number;
  validFrom: string;
  validTo: string;
  conditions: string;
  active: boolean;
  // Additional properties used in components
  featured?: boolean;
  startDate?: string;
  endDate?: string;
  usedCount?: number;
  maxUses?: number;
}

export interface BlogPost {
  id: string;
  title: string;
  slug?: string;
  excerpt: string;
  content: string;
  author: string;
  authorAvatar?: string;
  publishDate: string;
  category: string;
  tags: string[];
  image: string;
  readTime: number;
  views?: number;
  likes?: number;
}

export interface CampEvent {
  id: string;
  title: string;
  description: string;
  date: string;
  location: string;
  organizer: string;
  category: 'workshop' | 'social' | 'adventure' | 'conservation';
  maxParticipants: number;
  currentParticipants: number;
  price: number;
  image: string;
  tags: string[];
}

export interface ForumPost {
  id: string;
  title: string;
  content: string;
  authorId: string;
  authorName: string;
  authorAvatar?: string;
  category: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
  views: number;
  likes: number;
  replies: number;
  isSticky: boolean;
  isClosed: boolean;
  // Additional properties used in components
  userId?: string;
  userName?: string;
  userAvatar?: string;
  date?: string;
  solved?: boolean;
}

export interface ForumReply {
  id: string;
  postId: string;
  userId: string;
  userName: string;
  userAvatar?: string;
  content: string;
  date: string;
  likes: number;
  isAnswer: boolean;
}

export interface Campsite {
  id: string;
  name: string;
  location: string;
  province: string;
  type: 'tent' | 'caravan' | 'glamping' | 'chalet';
  price: number;
  rating: number;
  reviewCount: number;
  image: string;
  images: string[];
  description: string;
  amenities: string[];
  coordinates: { lat: number; lng: number };
  available: boolean;
  // Phase 2 enhancements
  providerId: string;
  featured: boolean;
  seasonalPricing?: {
    peak: number;
    offPeak: number;
    holiday: number;
  };
  capacity: {
    maxGuests: number;
    sites: number;
  };
  checkInTime: string;
  checkOutTime: string;
  policies: {
    cancellation: string;
    pets: boolean;
    smoking: boolean;
    parties: boolean;
  };
  nearbyAttractions: string[];
  accessibility: string[];
  badges?: string[];
}

export const mockCampsites: Campsite[] = [
  {
    id: '1',
    name: 'Drakensberg Mountain Retreat',
    location: 'Royal Natal National Park',
    province: 'KwaZulu-Natal',
    type: 'chalet',
    price: 850,
    rating: 4.8,
    reviewCount: 127,
    image: '/placeholder.svg',
    images: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    description: 'Experience the majestic Drakensberg mountains with our luxury chalets offering breathtaking views and modern amenities.',
    amenities: ['Electricity', 'Private Ablutions', 'Wi-Fi', 'Hiking Trails', 'Restaurant', 'Swimming Pool'],
    coordinates: { lat: -28.7282, lng: 28.9395 },
    available: true,
    providerId: 'provider-1',
    featured: true,
    seasonalPricing: {
      peak: 1200,
      offPeak: 650,
      holiday: 1400
    },
    capacity: {
      maxGuests: 4,
      sites: 12
    },
    checkInTime: '14:00',
    checkOutTime: '10:00',
    policies: {
      cancellation: 'Free cancellation up to 48 hours before check-in',
      pets: false,
      smoking: false,
      parties: false
    },
    nearbyAttractions: ['Amphitheatre', 'Tugela Falls', 'Mont-aux-Sources'],
    accessibility: ['Wheelchair accessible paths', 'Accessible ablutions'],
    badges: ['scenic-location', 'adventure-hub', 'wifi-available', 'swimming-pool', 'restaurant-onsite', 'certified-ablutions', 'safety-certified', 'tourism-graded', 'wheelchair-accessible']
  },
  {
    id: '2',
    name: 'Kruger Safari Glamping',
    location: 'Greater Kruger Area',
    province: 'Mpumalanga',
    type: 'glamping',
    price: 1200,
    rating: 4.9,
    reviewCount: 89,
    image: '/placeholder.svg',
    images: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    description: 'Luxury safari tents with en-suite bathrooms, private decks, and incredible wildlife viewing opportunities.',
    amenities: ['Electricity', 'Private Ablutions', 'Game Drives', 'Restaurant', 'Bar', 'Wi-Fi'],
    coordinates: { lat: -24.9870, lng: 31.5540 },
    available: true,
    providerId: 'provider-2',
    featured: true,
    seasonalPricing: {
      peak: 1800,
      offPeak: 900,
      holiday: 2000
    },
    capacity: {
      maxGuests: 2,
      sites: 8
    },
    checkInTime: '15:00',
    checkOutTime: '11:00',
    policies: {
      cancellation: 'Free cancellation up to 72 hours before check-in',
      pets: false,
      smoking: false,
      parties: false
    },
    nearbyAttractions: ['Kruger National Park', 'Blyde River Canyon', 'Bourke\'s Luck Potholes'],
    accessibility: ['Level pathways', 'Accessible viewing deck'],
    badges: ['luxury-glamping', 'wildlife-viewing', 'scenic-location', 'wifi-available', 'restaurant-onsite', 'certified-ablutions', 'safety-certified', 'tourism-graded']
  },
  {
    id: '3',
    name: 'Garden Route Coastal Chalets',
    location: 'Tsitsikamma National Park',
    province: 'Western Cape',
    type: 'chalet',
    price: 720,
    rating: 4.6,
    reviewCount: 203,
    image: '/placeholder.svg',
    images: ['/placeholder.svg', '/placeholder.svg', '/placeholder.svg'],
    description: 'Charming coastal chalets with stunning ocean views, perfect for families and couples seeking a peaceful getaway.',
    amenities: ['Ocean Views', 'Private Ablutions', 'Kitchen', 'Braai Area', 'Beach Access', 'Wi-Fi'],
    coordinates: { lat: -34.0522, lng: 23.8949 },
    available: true,
    providerId: 'provider-3',
    featured: true,
    seasonalPricing: {
      peak: 1000,
      offPeak: 550,
      holiday: 1150
    },
    capacity: {
      maxGuests: 6,
      sites: 15
    },
    checkInTime: '14:00',
    checkOutTime: '10:00',
    policies: {
      cancellation: 'Free cancellation up to 48 hours before check-in',
      pets: true,
      smoking: false,
      parties: false
    },
    nearbyAttractions: ['Otter Trail', 'Storms River Mouth', 'Canopy Tours'],
    accessibility: ['Beach wheelchair available', 'Accessible chalets'],
    badges: ['scenic-location', 'family-friendly', 'pet-friendly', 'wifi-available', 'adventure-hub', 'certified-ablutions', 'safety-certified', 'wheelchair-accessible']
  }
];

// Enhanced mock campsites for testing (simplified versions from main dataset)
export const simpleCampsites: Campsite[] = [
  {
    id: 'simple-1',
    name: 'Basic Mountain Camp',
    location: 'Drakensberg',
    province: 'KwaZulu-Natal',
    type: 'tent',
    price: 250,
    rating: 4.2,
    reviewCount: 45,
    image: '/placeholder.svg',
    images: ['/placeholder.svg'],
    description: 'Simple mountain camping experience',
    amenities: ['Fire Pits', 'Shared Ablutions'],
    coordinates: { lat: -28.7282, lng: 28.9395 },
    available: true,
    providerId: 'provider-1',
    featured: false,
    capacity: { maxGuests: 4, sites: 8 },
    checkInTime: '14:00',
    checkOutTime: '10:00',
    policies: { cancellation: 'Free cancellation up to 24 hours', pets: true, smoking: false, parties: false },
    nearbyAttractions: ['Mountain trails', 'Scenic views'],
    accessibility: ['Basic access'],
    badges: ['scenic-location', 'adventure-hub']
  },
  {
    id: 'simple-2',
    name: 'Coastal Chalets',
    location: 'Garden Route',
    province: 'Western Cape',
    type: 'chalet',
    price: 650,
    rating: 4.6,
    reviewCount: 78,
    image: '/placeholder.svg',
    images: ['/placeholder.svg'],
    description: 'Beautiful coastal chalets with ocean views',
    amenities: ['Ocean Views', 'Private Ablutions', 'Kitchen'],
    coordinates: { lat: -34.0522, lng: 23.0449 },
    available: true,
    providerId: 'provider-2',
    featured: true,
    capacity: { maxGuests: 6, sites: 5 },
    checkInTime: '15:00',
    checkOutTime: '11:00',
    policies: { cancellation: 'Free cancellation up to 48 hours', pets: false, smoking: false, parties: false },
    nearbyAttractions: ['Beach access', 'Coastal hiking'],
    accessibility: ['Wheelchair accessible'],
    badges: ['scenic-location', 'family-friendly', 'wheelchair-accessible']
  },
  {
    id: 'simple-3',
    name: 'Bush Camp',
    location: 'Magaliesberg',
    province: 'Gauteng',
    type: 'tent',
    price: 180,
    rating: 4.0,
    reviewCount: 32,
    image: '/placeholder.svg',
    images: ['/placeholder.svg'],
    description: 'Rustic bush camping experience',
    amenities: ['Fire Pits', 'Shared Ablutions', 'Hiking'],
    coordinates: { lat: -25.7461, lng: 27.8879 },
    available: true,
    providerId: 'provider-3',
    featured: false,
    capacity: { maxGuests: 2, sites: 15 },
    checkInTime: '14:00',
    checkOutTime: '10:00',
    policies: { cancellation: 'Free cancellation up to 24 hours', pets: true, smoking: true, parties: false },
    nearbyAttractions: ['Hiking trails', 'Rock formations'],
    accessibility: ['Basic access'],
    badges: ['adventure-hub', 'eco-friendly']
  },
  {
    id: 'simple-4',
    name: 'Luxury Resort',
    location: 'Hermanus',
    province: 'Western Cape',
    type: 'chalet',
    price: 950,
    rating: 4.8,
    reviewCount: 156,
    image: '/placeholder.svg',
    images: ['/placeholder.svg'],
    description: 'Luxury resort with all amenities',
    amenities: ['Swimming Pool', 'Restaurant', 'Spa', 'Wi-Fi'],
    coordinates: { lat: -34.4187, lng: 19.2345 },
    available: true,
    providerId: 'provider-4',
    featured: true,
    capacity: { maxGuests: 8, sites: 3 },
    checkInTime: '15:00',
    checkOutTime: '11:00',
    policies: { cancellation: 'Free cancellation up to 72 hours', pets: false, smoking: false, parties: false },
    nearbyAttractions: ['Whale watching', 'Wine estates'],
    accessibility: ['Wheelchair accessible', 'Accessible pools'],
    badges: ['luxury-glamping', 'scenic-location', 'wifi-available', 'swimming-pool', 'restaurant-onsite', 'wheelchair-accessible']
  }
];

// Combine all campsites for export
export const allCampsites = [...mockCampsites, ...simpleCampsites];