// Badge system configuration for campsites

export interface Badge {
  id: string;
  name: string;
  description: string;
  icon: string;
  color: string;
  bgColor: string;
  category: 'amenity' | 'certification' | 'experience' | 'accessibility' | 'eco';
  criteria: string;
}

export const availableBadges: Badge[] = [
  // Amenity Badges
  {
    id: 'pet-friendly',
    name: 'Pet-Friendly',
    description: 'Welcomes pets with dedicated facilities and areas',
    icon: '🐕',
    color: 'text-blue-800',
    bgColor: 'bg-blue-100',
    category: 'amenity',
    criteria: 'Allows pets and provides pet-specific amenities'
  },
  {
    id: 'family-friendly',
    name: 'Family-Friendly',
    description: 'Perfect for families with children of all ages',
    icon: '👨‍👩‍👧‍👦',
    color: 'text-green-800',
    bgColor: 'bg-green-100',
    category: 'amenity',
    criteria: 'Child-safe facilities, playground, family activities'
  },
  {
    id: 'wifi-available',
    name: 'Wi-Fi Available',
    description: 'Reliable internet connection throughout the site',
    icon: '📶',
    color: 'text-purple-800',
    bgColor: 'bg-purple-100',
    category: 'amenity',
    criteria: 'Free or paid Wi-Fi access for guests'
  },
  {
    id: 'swimming-pool',
    name: 'Swimming Pool',
    description: 'On-site swimming facilities for guests',
    icon: '🏊',
    color: 'text-cyan-800',
    bgColor: 'bg-cyan-100',
    category: 'amenity',
    criteria: 'Swimming pool or water recreation facilities'
  },
  {
    id: 'restaurant-onsite',
    name: 'Restaurant On-Site',
    description: 'Dining facilities available on the premises',
    icon: '🍽️',
    color: 'text-orange-800',
    bgColor: 'bg-orange-100',
    category: 'amenity',
    criteria: 'Restaurant, café, or food service on-site'
  },

  // Certification Badges
  {
    id: 'certified-ablutions',
    name: 'Certified Ablutions',
    description: 'Clean, well-maintained bathroom facilities',
    icon: '🚿',
    color: 'text-indigo-800',
    bgColor: 'bg-indigo-100',
    category: 'certification',
    criteria: 'Meets hygiene and cleanliness standards'
  },
  {
    id: 'safety-certified',
    name: 'Safety Certified',
    description: 'Meets all safety and security standards',
    icon: '🛡️',
    color: 'text-red-800',
    bgColor: 'bg-red-100',
    category: 'certification',
    criteria: 'Security measures, emergency procedures, safety equipment'
  },
  {
    id: 'tourism-graded',
    name: 'Tourism Graded',
    description: 'Officially graded by South African Tourism',
    icon: '⭐',
    color: 'text-yellow-800',
    bgColor: 'bg-yellow-100',
    category: 'certification',
    criteria: 'Official tourism grading certification'
  },

  // Experience Badges
  {
    id: 'luxury-glamping',
    name: 'Luxury Glamping',
    description: 'Premium glamping experience with high-end amenities',
    icon: '✨',
    color: 'text-pink-800',
    bgColor: 'bg-pink-100',
    category: 'experience',
    criteria: 'Luxury accommodations, premium amenities, exceptional service'
  },
  {
    id: 'adventure-hub',
    name: 'Adventure Hub',
    description: 'Gateway to outdoor adventures and activities',
    icon: '🏔️',
    color: 'text-emerald-800',
    bgColor: 'bg-emerald-100',
    category: 'experience',
    criteria: 'Access to hiking, climbing, water sports, or adventure activities'
  },
  {
    id: 'wildlife-viewing',
    name: 'Wildlife Viewing',
    description: 'Excellent opportunities for wildlife observation',
    icon: '🦁',
    color: 'text-amber-800',
    bgColor: 'bg-amber-100',
    category: 'experience',
    criteria: 'Located in or near wildlife areas, game viewing opportunities'
  },
  {
    id: 'scenic-location',
    name: 'Scenic Location',
    description: 'Breathtaking natural scenery and views',
    icon: '🌄',
    color: 'text-teal-800',
    bgColor: 'bg-teal-100',
    category: 'experience',
    criteria: 'Outstanding natural beauty, panoramic views, photogenic location'
  },

  // Accessibility Badges
  {
    id: 'wheelchair-accessible',
    name: 'Wheelchair Accessible',
    description: 'Fully accessible facilities for mobility-impaired guests',
    icon: '♿',
    color: 'text-blue-800',
    bgColor: 'bg-blue-100',
    category: 'accessibility',
    criteria: 'Wheelchair-accessible paths, facilities, and accommodations'
  },
  {
    id: 'senior-friendly',
    name: 'Senior-Friendly',
    description: 'Comfortable and convenient for older guests',
    icon: '👴',
    color: 'text-gray-800',
    bgColor: 'bg-gray-100',
    category: 'accessibility',
    criteria: 'Easy access, comfortable facilities, senior-oriented services'
  },

  // Eco Badges
  {
    id: 'eco-friendly',
    name: 'Eco-Friendly',
    description: 'Committed to environmental sustainability',
    icon: '🌱',
    color: 'text-green-800',
    bgColor: 'bg-green-100',
    category: 'eco',
    criteria: 'Solar power, water conservation, waste management, eco practices'
  },
  {
    id: 'carbon-neutral',
    name: 'Carbon Neutral',
    description: 'Operates with net-zero carbon emissions',
    icon: '🌍',
    color: 'text-emerald-800',
    bgColor: 'bg-emerald-100',
    category: 'eco',
    criteria: 'Carbon offset programs, renewable energy, sustainable operations'
  },
  {
    id: 'water-wise',
    name: 'Water Wise',
    description: 'Implements water conservation practices',
    icon: '💧',
    color: 'text-blue-800',
    bgColor: 'bg-blue-100',
    category: 'eco',
    criteria: 'Water-saving systems, rainwater harvesting, conservation education'
  }
];

// Utility functions for badges
export const getBadgeById = (id: string): Badge | undefined => 
  availableBadges.find(badge => badge.id === id);

export const getBadgesByCategory = (category: string): Badge[] => 
  availableBadges.filter(badge => badge.category === category);

export const getBadgesByIds = (ids: string[]): Badge[] => 
  ids.map(id => getBadgeById(id)).filter(Boolean) as Badge[];

// Badge assignment logic based on campsite properties
export const assignBadgesBasedOnCampsite = (campsite: any): string[] => {
  const badges: string[] = [];

  // Pet-friendly badge
  if (campsite.policies?.pets || campsite.amenities?.includes('Pet-Friendly')) {
    badges.push('pet-friendly');
  }

  // Family-friendly badge
  if (campsite.amenities?.includes('Playground') || 
      campsite.amenities?.includes('Family Activities') ||
      campsite.capacity?.maxGuests >= 6) {
    badges.push('family-friendly');
  }

  // Wi-Fi badge
  if (campsite.amenities?.includes('Wi-Fi') || campsite.amenities?.includes('Internet')) {
    badges.push('wifi-available');
  }

  // Swimming pool badge
  if (campsite.amenities?.includes('Swimming Pool') || campsite.amenities?.includes('Pool')) {
    badges.push('swimming-pool');
  }

  // Restaurant badge
  if (campsite.amenities?.includes('Restaurant') || campsite.amenities?.includes('Dining')) {
    badges.push('restaurant-onsite');
  }

  // Luxury glamping badge
  if (campsite.type === 'glamping' && campsite.price > 800) {
    badges.push('luxury-glamping');
  }

  // Adventure hub badge
  if (campsite.amenities?.includes('Hiking Trails') || 
      campsite.amenities?.includes('Adventure Activities') ||
      campsite.nearbyAttractions?.some((attr: string) => 
        attr.toLowerCase().includes('hiking') || 
        attr.toLowerCase().includes('climbing') ||
        attr.toLowerCase().includes('adventure')
      )) {
    badges.push('adventure-hub');
  }

  // Wildlife viewing badge
  if (campsite.location?.toLowerCase().includes('game') ||
      campsite.location?.toLowerCase().includes('safari') ||
      campsite.location?.toLowerCase().includes('kruger') ||
      campsite.nearbyAttractions?.some((attr: string) => 
        attr.toLowerCase().includes('wildlife') || 
        attr.toLowerCase().includes('game')
      )) {
    badges.push('wildlife-viewing');
  }

  // Scenic location badge
  if (campsite.location?.toLowerCase().includes('mountain') ||
      campsite.location?.toLowerCase().includes('coast') ||
      campsite.location?.toLowerCase().includes('river') ||
      campsite.rating >= 4.5) {
    badges.push('scenic-location');
  }

  // Eco-friendly badge
  if (campsite.amenities?.includes('Solar Power') || 
      campsite.amenities?.includes('Eco-Friendly') ||
      campsite.amenities?.includes('Recycling')) {
    badges.push('eco-friendly');
  }

  // Certified ablutions badge (assume all sites with good ratings have this)
  if (campsite.rating >= 4.0) {
    badges.push('certified-ablutions');
  }

  // Safety certified badge (assume featured sites are safety certified)
  if (campsite.featured || campsite.rating >= 4.5) {
    badges.push('safety-certified');
  }

  // Tourism graded badge (assume high-rated sites are tourism graded)
  if (campsite.rating >= 4.3 && campsite.reviewCount >= 50) {
    badges.push('tourism-graded');
  }

  // Wheelchair accessible badge
  if (campsite.accessibility?.includes('Wheelchair Accessible') ||
      campsite.amenities?.includes('Wheelchair Access')) {
    badges.push('wheelchair-accessible');
  }

  return badges;
};
