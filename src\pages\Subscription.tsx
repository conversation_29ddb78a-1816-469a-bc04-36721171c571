import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Progress } from '@/components/ui/progress';
import { 
  Check, 
  X, 
  Crown, 
  Star, 
  TrendingUp,
  Users,
  MessageSquare,
  Camera,
  BarChart3,
  Zap,
  Shield,
  Headphones
} from 'lucide-react';

const Subscription = () => {
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly');
  const [currentPlan, setCurrentPlan] = useState('free'); // free, premium, business

  const plans = [
    {
      id: 'free',
      name: 'Free',
      price: { monthly: 0, yearly: 0 },
      description: 'Perfect for getting started',
      features: [
        { name: 'List up to 2 campsites', included: true },
        { name: 'Basic listing photos (up to 5)', included: true },
        { name: 'Standard search visibility', included: true },
        { name: 'Basic messaging', included: true },
        { name: 'Mobile app access', included: true },
        { name: 'Community forum access', included: true },
        { name: 'Featured listings', included: false },
        { name: 'Advanced analytics', included: false },
        { name: 'Priority support', included: false },
        { name: 'Custom branding', included: false }
      ],
      limitations: [
        '5% commission on bookings',
        'Limited to 2 active listings',
        'Basic customer support'
      ],
      popular: false
    },
    {
      id: 'premium',
      name: 'Premium',
      price: { monthly: 299, yearly: 2990 },
      description: 'Best for growing campsite businesses',
      features: [
        { name: 'List up to 10 campsites', included: true },
        { name: 'Unlimited listing photos', included: true },
        { name: 'Priority search placement', included: true },
        { name: 'Advanced messaging & booking tools', included: true },
        { name: 'Mobile app access', included: true },
        { name: 'Community forum access', included: true },
        { name: 'Featured listings (2 per month)', included: true },
        { name: 'Advanced analytics dashboard', included: true },
        { name: 'Priority email support', included: true },
        { name: 'Custom branding', included: false }
      ],
      limitations: [
        '3% commission on bookings',
        'Up to 10 active listings',
        'Email support (24h response)'
      ],
      popular: true
    },
    {
      id: 'business',
      name: 'Business',
      price: { monthly: 599, yearly: 5990 },
      description: 'For established campsite networks',
      features: [
        { name: 'Unlimited campsites', included: true },
        { name: 'Unlimited listing photos & videos', included: true },
        { name: 'Top search placement', included: true },
        { name: 'Advanced messaging & booking tools', included: true },
        { name: 'Mobile app access', included: true },
        { name: 'Community forum access', included: true },
        { name: 'Unlimited featured listings', included: true },
        { name: 'Advanced analytics dashboard', included: true },
        { name: 'Priority phone & email support', included: true },
        { name: 'Custom branding & white-label', included: true }
      ],
      limitations: [
        '2% commission on bookings',
        'Unlimited listings',
        'Dedicated account manager'
      ],
      popular: false
    }
  ];

  const currentPlanData = plans.find(p => p.id === currentPlan);
  const yearlyDiscount = billingCycle === 'yearly' ? 17 : 0; // 17% discount for yearly

  const getPrice = (plan: typeof plans[0]) => {
    const price = plan.price[billingCycle];
    if (billingCycle === 'yearly') {
      return Math.round(price * (1 - yearlyDiscount / 100));
    }
    return price;
  };

  const handleUpgrade = (planId: string) => {
    console.log('Upgrading to plan:', planId);
    // In a real app, this would integrate with a payment processor
    setCurrentPlan(planId);
  };

  // Mock usage stats for current user
  const usageStats = {
    listings: { used: 2, limit: currentPlan === 'free' ? 2 : currentPlan === 'premium' ? 10 : 999 },
    photos: { used: 15, limit: currentPlan === 'free' ? 10 : 999 },
    featuredListings: { used: 0, limit: currentPlan === 'free' ? 0 : currentPlan === 'premium' ? 2 : 999 },
    monthlyBookings: 23,
    commission: currentPlan === 'free' ? 5 : currentPlan === 'premium' ? 3 : 2
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Choose Your Plan</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Grow your campsite business with the right tools and features
          </p>
        </div>

        {/* Current Plan Status */}
        {currentPlanData && (
          <Card className="mb-8">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center">
                    {currentPlan === 'free' && <Users className="h-6 w-6 text-primary" />}
                    {currentPlan === 'premium' && <Star className="h-6 w-6 text-primary" />}
                    {currentPlan === 'business' && <Crown className="h-6 w-6 text-primary" />}
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold">Current Plan: {currentPlanData.name}</h3>
                    <p className="text-muted-foreground">
                      {currentPlan === 'free' ? 'Free forever' : `R${getPrice(currentPlanData)}/${billingCycle === 'monthly' ? 'month' : 'year'}`}
                    </p>
                  </div>
                </div>
                
                {currentPlan !== 'business' && (
                  <Button>
                    Upgrade Plan
                  </Button>
                )}
              </div>
              
              {/* Usage Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Active Listings</span>
                    <span>{usageStats.listings.used}/{usageStats.listings.limit === 999 ? '∞' : usageStats.listings.limit}</span>
                  </div>
                  <Progress 
                    value={(usageStats.listings.used / usageStats.listings.limit) * 100} 
                    className="h-2"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Photos Used</span>
                    <span>{usageStats.photos.used}/{usageStats.photos.limit === 999 ? '∞' : usageStats.photos.limit}</span>
                  </div>
                  <Progress 
                    value={usageStats.photos.limit === 999 ? 15 : (usageStats.photos.used / usageStats.photos.limit) * 100} 
                    className="h-2"
                  />
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Commission Rate</span>
                    <span>{usageStats.commission}%</span>
                  </div>
                  <div className="text-xs text-muted-foreground">
                    R{Math.round(usageStats.monthlyBookings * 850 * (usageStats.commission / 100))} this month
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Billing Toggle */}
        <div className="flex items-center justify-center gap-4 mb-8">
          <span className={billingCycle === 'monthly' ? 'font-medium' : 'text-muted-foreground'}>
            Monthly
          </span>
          <Switch
            checked={billingCycle === 'yearly'}
            onCheckedChange={(checked) => setBillingCycle(checked ? 'yearly' : 'monthly')}
          />
          <span className={billingCycle === 'yearly' ? 'font-medium' : 'text-muted-foreground'}>
            Yearly
          </span>
          {billingCycle === 'yearly' && (
            <Badge variant="secondary" className="ml-2">
              Save {yearlyDiscount}%
            </Badge>
          )}
        </div>

        {/* Pricing Plans */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
          {plans.map(plan => (
            <Card key={plan.id} className={`relative ${plan.popular ? 'border-primary shadow-lg' : ''}`}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge className="bg-primary text-primary-foreground">
                    Most Popular
                  </Badge>
                </div>
              )}
              
              <CardHeader className="text-center">
                <div className="h-16 w-16 mx-auto mb-4 bg-primary/10 rounded-full flex items-center justify-center">
                  {plan.id === 'free' && <Users className="h-8 w-8 text-primary" />}
                  {plan.id === 'premium' && <Star className="h-8 w-8 text-primary" />}
                  {plan.id === 'business' && <Crown className="h-8 w-8 text-primary" />}
                </div>
                
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <p className="text-muted-foreground">{plan.description}</p>
                
                <div className="mt-4">
                  <span className="text-4xl font-bold">
                    R{getPrice(plan)}
                  </span>
                  {plan.price.monthly > 0 && (
                    <span className="text-muted-foreground">
                      /{billingCycle === 'monthly' ? 'month' : 'year'}
                    </span>
                  )}
                </div>
                
                {billingCycle === 'yearly' && plan.price.yearly > 0 && (
                  <p className="text-sm text-green-600 mt-2">
                    Save R{plan.price.yearly - getPrice(plan)} per year
                  </p>
                )}
              </CardHeader>
              
              <CardContent>
                <div className="space-y-3 mb-6">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-3">
                      {feature.included ? (
                        <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
                      ) : (
                        <X className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                      )}
                      <span className={feature.included ? '' : 'text-muted-foreground'}>
                        {feature.name}
                      </span>
                    </div>
                  ))}
                </div>
                
                <div className="space-y-2 mb-6 text-sm text-muted-foreground">
                  {plan.limitations.map((limitation, index) => (
                    <div key={index}>• {limitation}</div>
                  ))}
                </div>
                
                <Button 
                  className="w-full" 
                  variant={currentPlan === plan.id ? 'secondary' : plan.popular ? 'default' : 'outline'}
                  disabled={currentPlan === plan.id}
                  onClick={() => handleUpgrade(plan.id)}
                >
                  {currentPlan === plan.id ? 'Current Plan' : 
                   plan.id === 'free' ? 'Downgrade' : 'Upgrade'}
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Additional Features */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6 text-center">
              <BarChart3 className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Advanced Analytics</h3>
              <p className="text-sm text-muted-foreground">
                Track bookings, revenue, and guest insights
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <Zap className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Instant Bookings</h3>
              <p className="text-sm text-muted-foreground">
                Accept bookings automatically with smart pricing
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <Shield className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="font-semibold mb-2">Secure Payments</h3>
              <p className="text-sm text-muted-foreground">
                Protected transactions with instant payouts
              </p>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6 text-center">
              <Headphones className="h-12 w-12 text-primary mx-auto mb-4" />
              <h3 className="font-semibold mb-2">24/7 Support</h3>
              <p className="text-sm text-muted-foreground">
                Get help whenever you need it from our team
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Subscription;
