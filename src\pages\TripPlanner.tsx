import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MapPin, 
  Calendar, 
  Clock, 
  DollarSign,
  Route,
  Mountain,
  Camera,
  Users,
  Star,
  Filter,
  Search,
  Navigation as NavigationIcon,
  Fuel,
  Backpack
} from 'lucide-react';
import { mockItineraries, getFeaturedItineraries } from '@/data/mock-itineraries';
import { allCampsites } from '@/data';

const TripPlanner = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [selectedDuration, setSelectedDuration] = useState('all');
  const [selectedSeason, setSelectedSeason] = useState('all');
  const [sortBy, setSortBy] = useState('featured');

  const difficulties = ['easy', 'moderate', 'challenging'];
  const seasons = ['Spring', 'Summer', 'Autumn', 'Winter'];
  const durations = [
    { value: '3', label: '1-3 days' },
    { value: '7', label: '4-7 days' },
    { value: '14', label: '8-14 days' }
  ];

  // Filter and sort itineraries
  const filteredItineraries = mockItineraries
    .filter(itinerary => {
      const matchesSearch = itinerary.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           itinerary.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           itinerary.region.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesDifficulty = selectedDifficulty === 'all' || itinerary.difficulty === selectedDifficulty;
      const matchesDuration = selectedDuration === 'all' || itinerary.duration.days <= parseInt(selectedDuration);
      const matchesSeason = selectedSeason === 'all' || 
                           itinerary.bestSeason.some(s => s.toLowerCase() === selectedSeason.toLowerCase());
      
      return matchesSearch && matchesDifficulty && matchesDuration && matchesSeason;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'featured':
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
        case 'duration':
          return a.duration.days - b.duration.days;
        case 'difficulty':
          const difficultyOrder = { easy: 1, moderate: 2, challenging: 3 };
          return difficultyOrder[a.difficulty] - difficultyOrder[b.difficulty];
        case 'distance':
          return a.totalDistance - b.totalDistance;
        default:
          return 0;
      }
    });

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'challenging': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'easy': return '🟢';
      case 'moderate': return '🟡';
      case 'challenging': return '🔴';
      default: return '⚪';
    }
  };

  const getRecommendedCampsites = (campsiteIds: string[]) => {
    return campsiteIds.map(id => allCampsites.find(c => c.id === id)).filter(Boolean);
  };

  const featuredItineraries = getFeaturedItineraries();

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Plan Your Perfect Trip</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover curated itineraries and scenic routes across South Africa's most beautiful destinations
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-primary">{mockItineraries.length}</div>
              <div className="text-sm text-muted-foreground">Curated Routes</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{mockItineraries.filter(i => i.difficulty === 'easy').length}</div>
              <div className="text-sm text-muted-foreground">Family-Friendly</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{featuredItineraries.length}</div>
              <div className="text-sm text-muted-foreground">Featured Routes</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">9</div>
              <div className="text-sm text-muted-foreground">Provinces Covered</div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Itineraries */}
        {featuredItineraries.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">🌟 Featured Routes</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredItineraries.map(itinerary => (
                <Card key={itinerary.id} className="relative overflow-hidden border-2 border-primary/20">
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-primary text-primary-foreground">
                      Featured
                    </Badge>
                  </div>
                  
                  <div className="relative h-48 bg-muted">
                    <img 
                      src={itinerary.image} 
                      alt={itinerary.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-white/90">
                        <span className="mr-1">{getDifficultyIcon(itinerary.difficulty)}</span>
                        {itinerary.difficulty}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant="secondary">
                        <Calendar className="h-3 w-3 mr-1" />
                        {itinerary.duration.days} days
                      </Badge>
                      <Badge variant="secondary">
                        <Route className="h-3 w-3 mr-1" />
                        {itinerary.totalDistance}km
                      </Badge>
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-2">{itinerary.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                      {itinerary.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{itinerary.region}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <DollarSign className="h-4 w-4" />
                        <span>From R{itinerary.estimatedCost.budget.toLocaleString()}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Star className="h-4 w-4" />
                        <span>{itinerary.highlights.length} highlights</span>
                      </div>
                    </div>
                    
                    <Button className="w-full">
                      View Itinerary
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-card rounded-lg p-6 mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Filter className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Filter Routes</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search routes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedDifficulty} onValueChange={setSelectedDifficulty}>
              <SelectTrigger>
                <SelectValue placeholder="All Difficulties" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Difficulties</SelectItem>
                {difficulties.map(difficulty => (
                  <SelectItem key={difficulty} value={difficulty}>
                    {getDifficultyIcon(difficulty)} {difficulty}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedDuration} onValueChange={setSelectedDuration}>
              <SelectTrigger>
                <SelectValue placeholder="Any Duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Duration</SelectItem>
                {durations.map(duration => (
                  <SelectItem key={duration.value} value={duration.value}>{duration.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedSeason} onValueChange={setSelectedSeason}>
              <SelectTrigger>
                <SelectValue placeholder="Any Season" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Any Season</SelectItem>
                {seasons.map(season => (
                  <SelectItem key={season} value={season}>{season}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="featured">Featured First</SelectItem>
                <SelectItem value="duration">Duration (Short to Long)</SelectItem>
                <SelectItem value="difficulty">Difficulty (Easy to Hard)</SelectItem>
                <SelectItem value="distance">Distance (Short to Long)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* All Itineraries */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">All Routes ({filteredItineraries.length})</h2>
          </div>
          
          {filteredItineraries.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="text-6xl mb-4">🗺️</div>
              <h3 className="text-xl font-semibold mb-2">No routes found</h3>
              <p className="text-muted-foreground">
                Try adjusting your filters to find more routes.
              </p>
            </Card>
          ) : (
            <div className="space-y-6">
              {filteredItineraries.map(itinerary => {
                const recommendedCampsites = getRecommendedCampsites(itinerary.recommendedCampsites);
                
                return (
                  <Card key={itinerary.id} className="overflow-hidden">
                    <div className="md:flex">
                      <div className="md:w-1/3">
                        <div className="relative h-64 md:h-full bg-muted">
                          <img 
                            src={itinerary.image} 
                            alt={itinerary.title}
                            className="w-full h-full object-cover"
                          />
                          <div className="absolute top-4 left-4">
                            <Badge variant="secondary" className={getDifficultyColor(itinerary.difficulty)}>
                              <span className="mr-1">{getDifficultyIcon(itinerary.difficulty)}</span>
                              {itinerary.difficulty}
                            </Badge>
                          </div>
                          {itinerary.featured && (
                            <div className="absolute top-4 right-4">
                              <Badge className="bg-primary text-primary-foreground">
                                Featured
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="md:w-2/3 p-6">
                        <div className="flex flex-wrap gap-2 mb-3">
                          <Badge variant="outline">
                            <Calendar className="h-3 w-3 mr-1" />
                            {itinerary.duration.days} days, {itinerary.duration.nights} nights
                          </Badge>
                          <Badge variant="outline">
                            <Route className="h-3 w-3 mr-1" />
                            {itinerary.totalDistance}km
                          </Badge>
                          <Badge variant="outline">
                            <NavigationIcon className="h-3 w-3 mr-1" />
                            {itinerary.stops.length} stops
                          </Badge>
                        </div>
                        
                        <h3 className="text-xl font-semibold mb-2">{itinerary.title}</h3>
                        <p className="text-muted-foreground mb-4">
                          {itinerary.description}
                        </p>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                          <div>
                            <h4 className="font-medium mb-2">🎯 Highlights</h4>
                            <ul className="text-sm text-muted-foreground space-y-1">
                              {itinerary.highlights.slice(0, 3).map((highlight, index) => (
                                <li key={index}>• {highlight}</li>
                              ))}
                              {itinerary.highlights.length > 3 && (
                                <li>• +{itinerary.highlights.length - 3} more</li>
                              )}
                            </ul>
                          </div>
                          
                          <div>
                            <h4 className="font-medium mb-2">🏕️ Recommended Campsites</h4>
                            <div className="space-y-1">
                              {recommendedCampsites.slice(0, 2).map((campsite, index) => (
                                <div key={index} className="text-sm text-muted-foreground">
                                  • {campsite?.name} (R{campsite?.price}/night)
                                </div>
                              ))}
                              {recommendedCampsites.length > 2 && (
                                <div className="text-sm text-muted-foreground">
                                  • +{recommendedCampsites.length - 2} more options
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4" />
                              <span>From R{itinerary.estimatedCost.budget.toLocaleString()}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Fuel className="h-4 w-4" />
                              <span>{itinerary.fuelStops.length} fuel stops</span>
                            </div>
                          </div>
                          
                          <Button>
                            View Full Itinerary
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TripPlanner;
