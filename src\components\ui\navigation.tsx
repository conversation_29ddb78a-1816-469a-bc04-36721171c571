import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, User, Menu } from "lucide-react";
import { useState } from "react";

export function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <nav className="bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <div className="flex items-center space-x-2">
            <Tent className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold text-foreground">CampSpot SA</span>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="/" className="text-foreground hover:text-primary transition-smooth">
              Home
            </a>
            <a href="/search" className="text-foreground hover:text-primary transition-smooth">
              Search
            </a>
            <a href="/community" className="text-foreground hover:text-primary transition-smooth">
              Community
            </a>
            <a href="/deals" className="text-foreground hover:text-primary transition-colors">
              Deals
            </a>
            <a href="/events" className="text-foreground hover:text-primary transition-colors">
              Events
            </a>
            <a href="/plan-trip" className="text-foreground hover:text-primary transition-colors">
              Plan a Trip
            </a>
            <a href="/membership" className="text-foreground hover:text-primary transition-colors">
              Membership
            </a>
            <a href="/favorites" className="text-foreground hover:text-primary transition-colors">
              ❤️ Favorites
            </a>
            <a href="/profile" className="text-foreground hover:text-primary transition-smooth">
              Profile
            </a>
            <Button variant="outline" className="mr-2">
              <User className="h-4 w-4 mr-2" />
              Sign In
            </Button>
            <Button className="bg-primary hover:bg-primary/90">
              List Your Campsite
            </Button>
          </div>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-6 w-6" />
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="flex flex-col space-y-4">
              <a href="/" className="text-foreground hover:text-primary transition-smooth">
                Home
              </a>
              <a href="/search" className="text-foreground hover:text-primary transition-smooth">
                Search
              </a>
              <a href="/community" className="text-foreground hover:text-primary transition-smooth">
                Community
              </a>
              <a href="/profile" className="text-foreground hover:text-primary transition-smooth">
                Profile
              </a>
              <Button variant="outline" className="w-full justify-start">
                <User className="h-4 w-4 mr-2" />
                Sign In
              </Button>
              <Button className="w-full bg-primary hover:bg-primary/90">
                List Your Campsite
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}