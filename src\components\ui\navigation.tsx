import { <PERSON><PERSON> } from "@/components/ui/button";
import { Tent, User, Menu } from "lucide-react";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";

export function Navigation() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const handleSignIn = () => {
    // For now, navigate to profile page - in a real app this would open a sign-in modal
    navigate('/profile');
  };

  const handleListCampsite = () => {
    // Navigate to provider dashboard for listing campsites
    navigate('/provider');
  };

  return (
    <nav className="bg-background/95 backdrop-blur-sm border-b border-border sticky top-0 z-50">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <Tent className="h-8 w-8 text-primary" />
            <span className="text-xl font-bold text-foreground">CampSpot SA</span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-foreground hover:text-primary transition-smooth">
              Home
            </Link>
            <Link to="/search" className="text-foreground hover:text-primary transition-smooth">
              Search
            </Link>
            <Link to="/community" className="text-foreground hover:text-primary transition-smooth">
              Community
            </Link>
            <Link to="/deals" className="text-foreground hover:text-primary transition-colors">
              Deals
            </Link>
            <Link to="/events" className="text-foreground hover:text-primary transition-colors">
              Events
            </Link>
            <Link to="/plan-trip" className="text-foreground hover:text-primary transition-colors">
              Plan a Trip
            </Link>
            <Link to="/membership" className="text-foreground hover:text-primary transition-colors">
              Membership
            </Link>
            <Link to="/favorites" className="text-foreground hover:text-primary transition-colors">
              ❤️ Favorites
            </Link>
            <Link to="/profile" className="text-foreground hover:text-primary transition-smooth">
              Profile
            </Link>
            <Button variant="outline" className="mr-2" onClick={handleSignIn}>
              <User className="h-4 w-4 mr-2" />
              Sign In
            </Button>
            <Button className="bg-primary hover:bg-primary/90" onClick={handleListCampsite}>
              List Your Campsite
            </Button>
          </div>

          {/* Mobile menu button */}
          <Button
            variant="ghost"
            className="md:hidden"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="h-6 w-6" />
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-border">
            <div className="flex flex-col space-y-4">
              <Link to="/" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Home
              </Link>
              <Link to="/search" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Search
              </Link>
              <Link to="/community" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Community
              </Link>
              <Link to="/deals" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Deals
              </Link>
              <Link to="/events" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Events
              </Link>
              <Link to="/plan-trip" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Plan a Trip
              </Link>
              <Link to="/membership" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Membership
              </Link>
              <Link to="/favorites" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                ❤️ Favorites
              </Link>
              <Link to="/profile" className="text-foreground hover:text-primary transition-smooth" onClick={() => setIsMenuOpen(false)}>
                Profile
              </Link>
              <Button variant="outline" className="w-full justify-start" onClick={() => { handleSignIn(); setIsMenuOpen(false); }}>
                <User className="h-4 w-4 mr-2" />
                Sign In
              </Button>
              <Button className="w-full bg-primary hover:bg-primary/90" onClick={() => { handleListCampsite(); setIsMenuOpen(false); }}>
                List Your Campsite
              </Button>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}