import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import MapSearch from '@/components/MapSearch';
import { allCampsites } from '@/data';
import { Star, MapPin, Users, List, Map } from 'lucide-react';

const MapView = () => {
  const [viewMode, setViewMode] = useState<'map' | 'list'>('map');
  const [filteredCampsites, setFilteredCampsites] = useState(allCampsites);
  const [selectedCampsite, setSelectedCampsite] = useState<string | null>(null);

  const handleFilterChange = (filters: any) => {
    let results = allCampsites;
    
    if (filters.province) {
      results = results.filter(c => c.province === filters.province);
    }
    
    if (filters.priceRange !== 'all') {
      const [min, max] = filters.priceRange.split('-').map(Number);
      results = results.filter(c => c.price >= min && c.price <= max);
    }
    
    if (filters.type !== 'all') {
      results = results.filter(c => c.type === filters.type);
    }
    
    setFilteredCampsites(results);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold">Explore Campsites</h1>
            <p className="text-muted-foreground">Find your perfect camping spot on the interactive map</p>
          </div>
          
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'map' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('map')}
            >
              <Map className="h-4 w-4 mr-2" />
              Map View
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4 mr-2" />
              List View
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* Map/List View */}
          <div className="lg:col-span-3">
            {viewMode === 'map' ? (
              <MapSearch 
                onFilterChange={handleFilterChange}
                selectedCampsites={filteredCampsites}
              />
            ) : (
              <div className="space-y-4">
                {filteredCampsites.map(campsite => (
                  <Card key={campsite.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <CardContent className="p-0">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-0">
                        <div className="relative h-48 md:h-auto">
                          <img 
                            src={campsite.image} 
                            alt={campsite.name}
                            className="w-full h-full object-cover"
                          />
                          {campsite.featured && (
                            <Badge className="absolute top-4 left-4 bg-accent text-accent-foreground">
                              Featured
                            </Badge>
                          )}
                        </div>
                        
                        <div className="md:col-span-2 p-6">
                          <div className="flex items-start justify-between mb-2">
                            <h3 className="text-xl font-semibold">{campsite.name}</h3>
                            <div className="text-right">
                              <div className="text-2xl font-bold">R{campsite.price}</div>
                              <div className="text-sm text-muted-foreground">/night</div>
                            </div>
                          </div>
                          
                          <div className="flex items-center text-muted-foreground mb-3">
                            <MapPin className="h-4 w-4 mr-1" />
                            <span className="text-sm">{campsite.location}, {campsite.province}</span>
                          </div>
                          
                          <p className="text-muted-foreground mb-4 line-clamp-2">
                            {campsite.description}
                          </p>
                          
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-4">
                              <div className="flex items-center">
                                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                                <span className="font-medium">{campsite.rating}</span>
                                <span className="text-muted-foreground ml-1">({campsite.reviewCount})</span>
                              </div>
                              <div className="flex items-center">
                                <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                                <span className="text-sm">Up to {campsite.capacity?.maxGuests || 4} guests</span>
                              </div>
                            </div>
                            
                            <Button asChild>
                              <a href={`/campsite/${campsite.id}`}>View Details</a>
                            </Button>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardContent className="p-6">
                <h3 className="font-semibold mb-4">Quick Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Total Campsites</span>
                    <span className="font-medium">{filteredCampsites.length}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Average Rating</span>
                    <span className="font-medium">
                      {(filteredCampsites.reduce((acc, c) => acc + c.rating, 0) / filteredCampsites.length).toFixed(1)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Price Range</span>
                    <span className="font-medium">
                      R{Math.min(...filteredCampsites.map(c => c.price))} - R{Math.max(...filteredCampsites.map(c => c.price))}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MapView;