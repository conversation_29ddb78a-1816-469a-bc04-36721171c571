@tailwind base;
@tailwind components;
@tailwind utilities;

/* CampSpot SA Design System - Inspired by South African outdoors */

@layer base {
  :root {
    /* Nature-inspired color palette */
    --background: 30 15% 98%;
    --foreground: 140 12% 15%;

    --card: 0 0% 100%;
    --card-foreground: 140 12% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 140 12% 15%;

    /* Forest Green Primary */
    --primary: 140 70% 25%;
    --primary-foreground: 0 0% 98%;
    --primary-hover: 140 70% 20%;

    /* Earthy Secondary */
    --secondary: 25 30% 85%;
    --secondary-foreground: 140 12% 15%;

    /* Muted earth tones */
    --muted: 30 20% 92%;
    --muted-foreground: 140 8% 45%;

    /* Sunset accent */
    --accent: 20 85% 60%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 30 15% 88%;
    --input: 30 15% 88%;
    --ring: 140 70% 25%;

    /* Custom gradients */
    --gradient-hero: linear-gradient(135deg, hsl(140 70% 25%), hsl(120 60% 35%), hsl(100 50% 45%));
    --gradient-card: linear-gradient(180deg, hsl(0 0% 100%), hsl(30 20% 96%));
    --gradient-sunset: linear-gradient(135deg, hsl(20 85% 60%), hsl(35 80% 55%));
    
    /* Shadows */
    --shadow-nature: 0 10px 30px -10px hsl(140 70% 25% / 0.15);
    --shadow-card: 0 4px 20px -4px hsl(140 20% 50% / 0.1);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark nature theme */
    --background: 140 20% 8%;
    --foreground: 30 15% 95%;

    --card: 140 15% 12%;
    --card-foreground: 30 15% 95%;

    --popover: 140 15% 12%;
    --popover-foreground: 30 15% 95%;

    --primary: 140 60% 45%;
    --primary-foreground: 140 20% 8%;
    --primary-hover: 140 60% 50%;

    --secondary: 140 15% 20%;
    --secondary-foreground: 30 15% 95%;

    --muted: 140 10% 18%;
    --muted-foreground: 30 8% 65%;

    --accent: 20 75% 55%;
    --accent-foreground: 140 20% 8%;

    --destructive: 0 62.8% 50%;
    --destructive-foreground: 0 0% 98%;

    --border: 140 15% 20%;
    --input: 140 15% 20%;
    --ring: 140 60% 45%;

    /* Dark gradients */
    --gradient-hero: linear-gradient(135deg, hsl(140 40% 15%), hsl(120 35% 20%), hsl(100 30% 25%));
    --gradient-card: linear-gradient(180deg, hsl(140 15% 12%), hsl(140 10% 15%));
    --gradient-sunset: linear-gradient(135deg, hsl(20 75% 55%), hsl(35 70% 50%));
    
    --shadow-nature: 0 10px 30px -10px hsl(0 0% 0% / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(0 0% 0% / 0.2);
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  /* Utility classes for our design system */
  .gradient-hero {
    background: var(--gradient-hero);
  }
  
  .gradient-card {
    background: var(--gradient-card);
  }
  
  .gradient-sunset {
    background: var(--gradient-sunset);
  }
  
  .shadow-nature {
    box-shadow: var(--shadow-nature);
  }
  
  .shadow-card {
    box-shadow: var(--shadow-card);
  }
  
  .transition-smooth {
    transition: var(--transition-smooth);
  }
}
