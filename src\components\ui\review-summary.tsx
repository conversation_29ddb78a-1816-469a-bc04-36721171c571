import { Star } from 'lucide-react';
import { Progress } from '@/components/ui/progress';
import { getReviewsByCampsiteId } from '@/data';

interface ReviewSummaryProps {
  campsiteId: string;
  totalRating: number;
  totalReviews: number;
}

export function ReviewSummary({ campsiteId, totalRating, totalReviews }: ReviewSummaryProps) {
  const reviews = getReviewsByCampsiteId(campsiteId);
  
  // Calculate rating distribution
  const ratingCounts = [0, 0, 0, 0, 0]; // Index 0 = 1 star, Index 4 = 5 stars
  reviews.forEach(review => {
    ratingCounts[review.rating - 1]++;
  });

  const ratingPercentages = ratingCounts.map(count => 
    totalReviews > 0 ? (count / totalReviews) * 100 : 0
  );

  return (
    <div className="space-y-4">
      {/* Overall Rating */}
      <div className="flex items-center gap-4">
        <div className="text-center">
          <div className="text-4xl font-bold">{totalRating}</div>
          <div className="flex justify-center mb-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`h-4 w-4 ${
                  star <= Math.round(totalRating)
                    ? 'fill-yellow-400 text-yellow-400'
                    : 'text-gray-300'
                }`}
              />
            ))}
          </div>
          <div className="text-sm text-muted-foreground">
            {totalReviews} review{totalReviews !== 1 ? 's' : ''}
          </div>
        </div>
        
        {/* Rating Breakdown */}
        <div className="flex-1 space-y-2">
          {[5, 4, 3, 2, 1].map((rating) => (
            <div key={rating} className="flex items-center gap-2 text-sm">
              <span className="w-8">{rating}</span>
              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
              <Progress 
                value={ratingPercentages[rating - 1]} 
                className="flex-1 h-2"
              />
              <span className="w-8 text-muted-foreground">
                {ratingCounts[rating - 1]}
              </span>
            </div>
          ))}
        </div>
      </div>
      
      {/* Review Highlights */}
      {reviews.length > 0 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
          <div>
            <h4 className="font-medium mb-2">Most mentioned positives</h4>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div>• Beautiful scenery and views</div>
              <div>• Clean facilities</div>
              <div>• Friendly and helpful staff</div>
            </div>
          </div>
          <div>
            <h4 className="font-medium mb-2">Areas for improvement</h4>
            <div className="space-y-1 text-sm text-muted-foreground">
              <div>• Wi-Fi connectivity could be better</div>
              <div>• More activities for children</div>
              <div>• Booking process clarity</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
