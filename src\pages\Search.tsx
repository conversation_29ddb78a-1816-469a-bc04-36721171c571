import { useState, useMemo } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { WishlistButton } from '@/components/ui/wishlist-button';
import { 
  Search as SearchIcon, 
  Filter, 
  Star, 
  MapPin, 
  Users, 
  Wifi, 
  Zap, 
  Car,
  Waves,
  TreePine,
  Utensils,
  Mountain
} from 'lucide-react';
import { 
  allCampsites, 
  SA_PROVINCES, 
  CAMPSITE_TYPES, 
  COMMON_AMENITIES,
  searchCampsites,
  filterCampsitesByAmenities,
  filterCampsitesByPriceRange,
  sortCampsites,
  getCampsitesByProvince,
  getCampsitesByType
} from '@/data';

const Search = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('');
  const [selectedType, setSelectedType] = useState('');
  const [priceRange, setPriceRange] = useState([0, 2000]);
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<'price-low' | 'price-high' | 'rating' | 'popular'>('rating');
  const [showFeaturedOnly, setShowFeaturedOnly] = useState(false);

  // Filter and search logic
  const filteredCampsites = useMemo(() => {
    let results = allCampsites;

    // Text search
    if (searchQuery) {
      results = searchCampsites(searchQuery);
    }

    // Province filter
    if (selectedProvince) {
      results = results.filter(campsite => campsite.province === selectedProvince);
    }

    // Type filter
    if (selectedType) {
      results = results.filter(campsite => campsite.type === selectedType);
    }

    // Price range filter
    results = results.filter(campsite => 
      campsite.price >= priceRange[0] && campsite.price <= priceRange[1]
    );

    // Amenities filter
    if (selectedAmenities.length > 0) {
      results = results.filter(campsite =>
        selectedAmenities.every(amenity => campsite.amenities.includes(amenity))
      );
    }

    // Featured only filter
    if (showFeaturedOnly) {
      results = results.filter(campsite => campsite.featured);
    }

    // Sort results
    return sortCampsites(results, sortBy);
  }, [searchQuery, selectedProvince, selectedType, priceRange, selectedAmenities, sortBy, showFeaturedOnly]);

  const handleAmenityToggle = (amenity: string) => {
    setSelectedAmenities(prev => 
      prev.includes(amenity) 
        ? prev.filter(a => a !== amenity)
        : [...prev, amenity]
    );
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedProvince('');
    setSelectedType('');
    setPriceRange([0, 2000]);
    setSelectedAmenities([]);
    setShowFeaturedOnly(false);
  };

  const getAmenityIcon = (amenity: string) => {
    switch (amenity) {
      case 'Wi-Fi': return <Wifi className="h-3 w-3" />;
      case 'Electricity': return <Zap className="h-3 w-3" />;
      case 'Private Ablutions': return <Car className="h-3 w-3" />;
      case 'Swimming Pool': return <Waves className="h-3 w-3" />;
      case 'Hiking Trails': return <Mountain className="h-3 w-3" />;
      case 'Restaurant': return <Utensils className="h-3 w-3" />;
      case 'Braai Facilities': return <TreePine className="h-3 w-3" />;
      default: return null;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Search Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">Find Your Perfect Campsite</h1>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1 relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search by name, location, or description..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            
            {/* Mobile Filter Button */}
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" className="md:hidden">
                  <Filter className="h-4 w-4 mr-2" />
                  Filters
                </Button>
              </SheetTrigger>
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Filter Campsites</SheetTitle>
                  <SheetDescription>
                    Refine your search with these filters
                  </SheetDescription>
                </SheetHeader>
                <div className="mt-6">
                  {/* Mobile filters content will go here */}
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Desktop Filters Sidebar */}
          <div className="hidden lg:block space-y-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="font-semibold">Filters</h3>
                  <Button variant="ghost" size="sm" onClick={clearFilters}>
                    Clear All
                  </Button>
                </div>

                {/* Province Filter */}
                <div className="space-y-3 mb-6">
                  <label className="text-sm font-medium">Province</label>
                  <Select value={selectedProvince} onValueChange={setSelectedProvince}>
                    <SelectTrigger>
                      <SelectValue placeholder="All provinces" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All provinces</SelectItem>
                      {SA_PROVINCES.map(province => (
                        <SelectItem key={province} value={province}>
                          {province}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Type Filter */}
                <div className="space-y-3 mb-6">
                  <label className="text-sm font-medium">Accommodation Type</label>
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">All types</SelectItem>
                      {CAMPSITE_TYPES.map(type => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Price Range */}
                <div className="space-y-3 mb-6">
                  <label className="text-sm font-medium">
                    Price Range: R{priceRange[0]} - R{priceRange[1]} per night
                  </label>
                  <Slider
                    value={priceRange}
                    onValueChange={setPriceRange}
                    max={2000}
                    min={0}
                    step={50}
                    className="w-full"
                  />
                </div>

                {/* Featured Only */}
                <div className="flex items-center space-x-2 mb-6">
                  <Checkbox
                    id="featured"
                    checked={showFeaturedOnly}
                    onCheckedChange={(checked) => setShowFeaturedOnly(!!checked)}
                  />
                  <label htmlFor="featured" className="text-sm font-medium">
                    Featured campsites only
                  </label>
                </div>

                {/* Amenities Filter */}
                <div className="space-y-3">
                  <label className="text-sm font-medium">Amenities</label>
                  <div className="space-y-2 max-h-48 overflow-y-auto">
                    {COMMON_AMENITIES.map(amenity => (
                      <div key={amenity} className="flex items-center space-x-2">
                        <Checkbox
                          id={amenity}
                          checked={selectedAmenities.includes(amenity)}
                          onCheckedChange={() => handleAmenityToggle(amenity)}
                        />
                        <label htmlFor={amenity} className="text-sm flex items-center gap-2">
                          {getAmenityIcon(amenity)}
                          {amenity}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <p className="text-muted-foreground">
                {filteredCampsites.length} campsite{filteredCampsites.length !== 1 ? 's' : ''} found
              </p>
              <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                <SelectTrigger className="w-48">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                  <SelectItem value="popular">Most Popular</SelectItem>
                  <SelectItem value="price-low">Lowest Price</SelectItem>
                  <SelectItem value="price-high">Highest Price</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Results Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {filteredCampsites.map(campsite => (
                <Card key={campsite.id} className="overflow-hidden hover:shadow-lg transition-shadow cursor-pointer">
                  <a href={`/campsite/${campsite.id}`} className="block">
                  <div className="relative h-48">
                    <img 
                      src={campsite.image} 
                      alt={campsite.name}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 right-4 flex gap-2">
                      <WishlistButton campsiteId={campsite.id} />
                      <div className="bg-card/90 backdrop-blur-sm rounded-full px-3 py-1">
                        <span className="text-sm font-semibold">R{campsite.price}/night</span>
                      </div>
                    </div>
                    {campsite.featured && (
                      <Badge className="absolute top-4 left-4 bg-accent text-accent-foreground">
                        Featured
                      </Badge>
                    )}
                  </div>
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-semibold text-lg">{campsite.name}</h3>
                      <div className="flex items-center">
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                        <span className="text-sm font-medium">{campsite.rating}</span>
                        <span className="text-sm text-muted-foreground ml-1">
                          ({campsite.reviewCount})
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex items-center text-muted-foreground mb-3">
                      <MapPin className="h-4 w-4 mr-1" />
                      <span className="text-sm">{campsite.location}, {campsite.province}</span>
                    </div>
                    
                    <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                      {campsite.description}
                    </p>
                    
                    <div className="flex items-center mb-3">
                      <Users className="h-4 w-4 mr-1 text-muted-foreground" />
                      <span className="text-sm text-muted-foreground">
                        Up to {campsite.capacity?.maxGuests || 4} guests
                      </span>
                    </div>
                    
                    <div className="flex flex-wrap gap-1">
                      {campsite.amenities.slice(0, 3).map(amenity => (
                        <Badge key={amenity} variant="secondary" className="text-xs">
                          {getAmenityIcon(amenity)}
                          <span className="ml-1">{amenity}</span>
                        </Badge>
                      ))}
                      {campsite.amenities.length > 3 && (
                        <Badge variant="outline" className="text-xs">
                          +{campsite.amenities.length - 3} more
                        </Badge>
                      )}
                    </div>
                  </CardContent>
                  </a>
                </Card>
              ))}
            </div>

            {/* No Results */}
            {filteredCampsites.length === 0 && (
              <div className="text-center py-12">
                <SearchIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-semibold mb-2">No campsites found</h3>
                <p className="text-muted-foreground mb-4">
                  Try adjusting your filters or search terms
                </p>
                <Button onClick={clearFilters}>Clear all filters</Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Search;
