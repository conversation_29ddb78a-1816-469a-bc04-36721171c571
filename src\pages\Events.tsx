import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Calendar, 
  MapPin, 
  Users, 
  Clock, 
  Star,
  Phone,
  Mail,
  Globe,
  Filter,
  Search,
  Ticket,
  Gift
} from 'lucide-react';
import { mockEvents, getFeaturedEvents, getUpcomingEvents } from '@/data/mock-events';

const Events = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedProvince, setSelectedProvince] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedPricing, setSelectedPricing] = useState('all');
  const [sortBy, setSortBy] = useState('date');

  const provinces = [
    'Western Cape', 'KwaZulu-Natal', 'Gauteng', 'Mpumalanga', 
    'Limpopo', 'Eastern Cape', 'Free State', 'North West', 'Northern Cape'
  ];

  const eventTypes = [
    { value: 'music_festival', label: 'Music Festival' },
    { value: 'hiking_weekend', label: 'Hiking Weekend' },
    { value: 'fishing_competition', label: 'Fishing Competition' },
    { value: 'camping_rally', label: 'Camping Rally' },
    { value: 'wildlife_viewing', label: 'Wildlife Viewing' },
    { value: 'cultural_festival', label: 'Cultural Festival' },
    { value: 'adventure_race', label: 'Adventure Race' },
    { value: 'stargazing', label: 'Stargazing' },
    { value: 'photography_workshop', label: 'Photography Workshop' }
  ];

  // Filter and sort events
  const filteredEvents = mockEvents
    .filter(event => {
      const matchesSearch = event.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           event.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           event.location.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesProvince = selectedProvince === 'all' || event.province === selectedProvince;
      const matchesType = selectedType === 'all' || event.eventType === selectedType;
      const matchesPricing = selectedPricing === 'all' || 
                            (selectedPricing === 'free' && event.isFree) ||
                            (selectedPricing === 'paid' && !event.isFree);
      const isUpcoming = new Date(event.startDate) >= new Date();
      
      return matchesSearch && matchesProvince && matchesType && matchesPricing && isUpcoming;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'date':
          return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();
        case 'price':
          const priceA = a.isFree ? 0 : (a.ticketPrice || 0);
          const priceB = b.isFree ? 0 : (b.ticketPrice || 0);
          return priceA - priceB;
        case 'popularity':
          return b.currentAttendees - a.currentAttendees;
        case 'featured':
          return (b.featured ? 1 : 0) - (a.featured ? 1 : 0);
        default:
          return 0;
      }
    });

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'music_festival': return '🎵';
      case 'hiking_weekend': return '🥾';
      case 'fishing_competition': return '🎣';
      case 'camping_rally': return '🏕️';
      case 'wildlife_viewing': return '🦁';
      case 'cultural_festival': return '🎭';
      case 'adventure_race': return '🏃';
      case 'stargazing': return '⭐';
      case 'photography_workshop': return '📸';
      default: return '📅';
    }
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case 'easy': return 'bg-green-100 text-green-800';
      case 'moderate': return 'bg-yellow-100 text-yellow-800';
      case 'challenging': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-ZA', {
      weekday: 'short',
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getDaysUntilEvent = (startDate: string) => {
    const today = new Date();
    const eventDate = new Date(startDate);
    const diffTime = eventDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const featuredEvents = getFeaturedEvents();
  const upcomingEvents = getUpcomingEvents(7);

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-4">Community Events Calendar</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Join fellow outdoor enthusiasts at exciting events across South Africa
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-primary">{upcomingEvents.length}</div>
              <div className="text-sm text-muted-foreground">This Week</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-green-600">{mockEvents.filter(e => e.isFree).length}</div>
              <div className="text-sm text-muted-foreground">Free Events</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-blue-600">{featuredEvents.length}</div>
              <div className="text-sm text-muted-foreground">Featured</div>
            </CardContent>
          </Card>
          <Card className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-purple-600">9</div>
              <div className="text-sm text-muted-foreground">Provinces</div>
            </CardContent>
          </Card>
        </div>

        {/* Featured Events */}
        {featuredEvents.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold mb-6">⭐ Featured Events</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredEvents.slice(0, 3).map(event => (
                <Card key={event.id} className="relative overflow-hidden border-2 border-primary/20">
                  <div className="absolute top-4 right-4 z-10">
                    <Badge className="bg-primary text-primary-foreground">
                      Featured
                    </Badge>
                  </div>
                  
                  <div className="relative h-48 bg-muted">
                    <img 
                      src={event.image} 
                      alt={event.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-white/90">
                        <span className="mr-1">{getEventTypeIcon(event.eventType)}</span>
                        {eventTypes.find(t => t.value === event.eventType)?.label}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      {event.isFree ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Gift className="h-3 w-3 mr-1" />
                          FREE
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <Ticket className="h-3 w-3 mr-1" />
                          R{event.ticketPrice}
                        </Badge>
                      )}
                      {event.difficulty && (
                        <Badge variant="secondary" className={getDifficultyColor(event.difficulty)}>
                          {event.difficulty}
                        </Badge>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-2">{event.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                      {event.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(event.startDate)}</span>
                        {event.endDate !== event.startDate && (
                          <span> - {formatDate(event.endDate)}</span>
                        )}
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Users className="h-4 w-4" />
                        <span>{event.currentAttendees} attending</span>
                        {event.maxAttendees && (
                          <span> • {event.maxAttendees - event.currentAttendees} spots left</span>
                        )}
                      </div>
                    </div>
                    
                    <Button className="w-full">
                      {event.bookingRequired ? 'Book Now' : 'Learn More'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="bg-card rounded-lg p-6 mb-8">
          <div className="flex items-center gap-2 mb-4">
            <Filter className="h-5 w-5" />
            <h3 className="text-lg font-semibold">Filter Events</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search events..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={selectedProvince} onValueChange={setSelectedProvince}>
              <SelectTrigger>
                <SelectValue placeholder="All Provinces" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Provinces</SelectItem>
                {provinces.map(province => (
                  <SelectItem key={province} value={province}>{province}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedType} onValueChange={setSelectedType}>
              <SelectTrigger>
                <SelectValue placeholder="All Types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {eventTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>{type.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            <Select value={selectedPricing} onValueChange={setSelectedPricing}>
              <SelectTrigger>
                <SelectValue placeholder="All Pricing" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Events</SelectItem>
                <SelectItem value="free">Free Events</SelectItem>
                <SelectItem value="paid">Paid Events</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date (Earliest)</SelectItem>
                <SelectItem value="price">Price (Low to High)</SelectItem>
                <SelectItem value="popularity">Most Popular</SelectItem>
                <SelectItem value="featured">Featured First</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* All Events */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">All Events ({filteredEvents.length})</h2>
          </div>
          
          {filteredEvents.length === 0 ? (
            <Card className="p-12 text-center">
              <div className="text-6xl mb-4">📅</div>
              <h3 className="text-xl font-semibold mb-2">No events found</h3>
              <p className="text-muted-foreground">
                Try adjusting your filters to find more events.
              </p>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredEvents.map(event => (
                <Card key={event.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  <div className="relative h-48 bg-muted">
                    <img 
                      src={event.image} 
                      alt={event.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge variant="secondary" className="bg-white/90">
                        <span className="mr-1">{getEventTypeIcon(event.eventType)}</span>
                        {eventTypes.find(t => t.value === event.eventType)?.label}
                      </Badge>
                    </div>
                    {getDaysUntilEvent(event.startDate) <= 7 && (
                      <div className="absolute top-4 right-4">
                        <Badge className="bg-red-500 text-white">
                          {getDaysUntilEvent(event.startDate)} days left
                        </Badge>
                      </div>
                    )}
                  </div>
                  
                  <CardContent className="p-6">
                    <div className="flex items-center gap-2 mb-2">
                      {event.isFree ? (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Gift className="h-3 w-3 mr-1" />
                          FREE
                        </Badge>
                      ) : (
                        <Badge variant="secondary">
                          <Ticket className="h-3 w-3 mr-1" />
                          R{event.ticketPrice}
                        </Badge>
                      )}
                      {event.difficulty && (
                        <Badge variant="secondary" className={getDifficultyColor(event.difficulty)}>
                          {event.difficulty}
                        </Badge>
                      )}
                    </div>
                    
                    <h3 className="text-lg font-semibold mb-2">{event.title}</h3>
                    <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                      {event.description}
                    </p>
                    
                    <div className="space-y-2 mb-4">
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Calendar className="h-4 w-4" />
                        <span>{formatDate(event.startDate)}</span>
                        {event.endDate !== event.startDate && (
                          <span> - {formatDate(event.endDate)}</span>
                        )}
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <MapPin className="h-4 w-4" />
                        <span>{event.location}</span>
                      </div>
                      <div className="flex items-center gap-1 text-sm text-muted-foreground">
                        <Users className="h-4 w-4" />
                        <span>{event.currentAttendees} attending</span>
                      </div>
                    </div>
                    
                    <Button className="w-full" variant="outline">
                      {event.bookingRequired ? 'Book Now' : 'Learn More'}
                    </Button>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Events;
