import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { 
  User, 
  MapPin, 
  Calendar, 
  Star, 
  Camera, 
  Edit, 
  Heart,
  MessageSquare,
  Award,
  TrendingUp,
  Settings
} from 'lucide-react';
import { mockUsers } from '@/data/mock-users';
import { allCampsites } from '@/data';

interface UserProfileProps {
  userId?: string;
  isOwnProfile?: boolean;
}

const UserProfile = ({ userId = 'user-1', isOwnProfile = true }: UserProfileProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('overview');
  
  const user = mockUsers.find(u => u.id === userId) || mockUsers[0];
  const userFavorites = allCampsites.filter(c => user.favorites.includes(c.id));
  
  // Mock user stats
  const userStats = {
    totalTrips: 8,
    nightsCamped: 24,
    favoriteProvince: 'Western Cape',
    joinedDate: user.joinDate,
    reviewsWritten: user.reviewCount,
    helpfulVotes: 45
  };

  const achievements = [
    { id: 1, name: 'Explorer', description: 'Visited 5+ provinces', earned: true, icon: '🌍' },
    { id: 2, name: 'Reviewer', description: 'Written 10+ reviews', earned: true, icon: '⭐' },
    { id: 3, name: 'Adventure Seeker', description: 'Stayed 20+ nights', earned: true, icon: '🏕️' },
    { id: 4, name: 'Community Helper', description: '50+ helpful votes', earned: false, icon: '🤝' },
    { id: 5, name: 'Early Bird', description: 'Member for 1+ year', earned: true, icon: '🐦' }
  ];

  const recentActivity = [
    { type: 'review', action: 'Reviewed Drakensberg Mountain Retreat', date: '2 days ago' },
    { type: 'favorite', action: 'Added Garden Route Coastal Chalets to favorites', date: '1 week ago' },
    { type: 'trip', action: 'Completed stay at Kruger Safari Glamping', date: '2 weeks ago' },
    { type: 'review', action: 'Reviewed Addo Elephant Bush Camp', date: '3 weeks ago' }
  ];

  const tabs = [
    { id: 'overview', label: 'Overview', icon: User },
    { id: 'favorites', label: 'Favorites', icon: Heart },
    { id: 'reviews', label: 'Reviews', icon: MessageSquare },
    { id: 'achievements', label: 'Achievements', icon: Award },
    ...(isOwnProfile ? [{ id: 'settings', label: 'Settings', icon: Settings }] : [])
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Stats Cards */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{userStats.totalTrips}</div>
                  <div className="text-sm text-muted-foreground">Total Trips</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{userStats.nightsCamped}</div>
                  <div className="text-sm text-muted-foreground">Nights Camped</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{userStats.reviewsWritten}</div>
                  <div className="text-sm text-muted-foreground">Reviews Written</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-primary">{userStats.helpfulVotes}</div>
                  <div className="text-sm text-muted-foreground">Helpful Votes</div>
                </CardContent>
              </Card>
            </div>

            {/* Recent Activity */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Activity</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity, index) => (
                    <div key={index} className="flex items-center gap-3">
                      <div className={`w-2 h-2 rounded-full ${
                        activity.type === 'review' ? 'bg-blue-500' :
                        activity.type === 'favorite' ? 'bg-red-500' :
                        'bg-green-500'
                      }`} />
                      <div className="flex-1">
                        <p className="text-sm">{activity.action}</p>
                        <p className="text-xs text-muted-foreground">{activity.date}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        );

      case 'favorites':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {userFavorites.map(campsite => (
              <Card key={campsite.id} className="overflow-hidden">
                <div className="relative h-32">
                  <img 
                    src={campsite.image} 
                    alt={campsite.name}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-4">
                  <h3 className="font-semibold mb-1">{campsite.name}</h3>
                  <div className="flex items-center text-muted-foreground text-sm mb-2">
                    <MapPin className="h-3 w-3 mr-1" />
                    {campsite.location}, {campsite.province}
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 mr-1" />
                      <span className="text-sm">{campsite.rating}</span>
                    </div>
                    <span className="text-sm font-semibold">R{campsite.price}/night</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        );

      case 'achievements':
        return (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {achievements.map(achievement => (
              <Card key={achievement.id} className={`${achievement.earned ? 'border-primary/50 bg-primary/5' : 'opacity-60'}`}>
                <CardContent className="p-4">
                  <div className="flex items-center gap-3">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div className="flex-1">
                      <h3 className="font-semibold">{achievement.name}</h3>
                      <p className="text-sm text-muted-foreground">{achievement.description}</p>
                    </div>
                    {achievement.earned && (
                      <Badge variant="secondary">Earned</Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        );

      case 'settings':
        return isOwnProfile ? (
          <Card>
            <CardHeader>
              <CardTitle>Profile Settings</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input id="name" defaultValue={user.name} />
                </div>
                <div>
                  <Label htmlFor="email">Email</Label>
                  <Input id="email" type="email" defaultValue={user.email} />
                </div>
              </div>
              
              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea id="bio" placeholder="Tell us about yourself..." />
              </div>
              
              <div>
                <Label htmlFor="location">Location</Label>
                <Input id="location" placeholder="Your city, province" />
              </div>
              
              <Button>Save Changes</Button>
            </CardContent>
          </Card>
        ) : null;

      default:
        return <div>Reviews content coming soon...</div>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Profile Header */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-6">
            <div className="relative">
              <Avatar className="w-24 h-24">
                <AvatarImage src={user.avatar} />
                <AvatarFallback className="text-lg">{user.name.charAt(0)}</AvatarFallback>
              </Avatar>
              {isOwnProfile && (
                <Button size="sm" variant="secondary" className="absolute -bottom-2 -right-2 w-8 h-8 rounded-full p-0">
                  <Camera className="h-3 w-3" />
                </Button>
              )}
            </div>
            
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                <h1 className="text-2xl font-bold">{user.name}</h1>
                {user.verified && (
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    <Star className="h-3 w-3 mr-1" />
                    Verified
                  </Badge>
                )}
                <Badge variant="outline">{user.subscriptionTier}</Badge>
              </div>
              
              <div className="flex items-center text-muted-foreground text-sm mb-3">
                <Calendar className="h-4 w-4 mr-1" />
                Joined {new Date(user.joinDate).toLocaleDateString('en-ZA', { 
                  year: 'numeric', 
                  month: 'long' 
                })}
              </div>
              
              {/* User Badges */}
              <div className="flex flex-wrap gap-2 mb-4">
                {user.badges.map(badge => (
                  <Badge key={badge} variant="secondary">{badge}</Badge>
                ))}
              </div>
              
              {isOwnProfile ? (
                <Button variant="outline">
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Profile
                </Button>
              ) : (
                <div className="flex gap-2">
                  <Button>Send Message</Button>
                  <Button variant="outline">Follow</Button>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabs */}
      <div className="border-b">
        <div className="flex space-x-8">
          {tabs.map(tab => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-1 py-4 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-muted-foreground hover:text-foreground'
                }`}
              >
                <Icon className="h-4 w-4" />
                {tab.label}
              </button>
            );
          })}
        </div>
      </div>

      {/* Tab Content */}
      <div>
        {renderTabContent()}
      </div>
    </div>
  );
};

export default UserProfile;