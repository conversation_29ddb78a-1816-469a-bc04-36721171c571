import { useState } from 'react';
import { Navigation } from '@/components/ui/navigation';
import UserProfile from '@/components/UserProfile';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Star, 
  MapPin, 
  Heart, 
  Calendar, 
  Settings, 
  User, 
  Bell,
  Shield,
  CreditCard,
  Award,
  Trash2,
  Edit
} from 'lucide-react';
import { 
  mockUsers, 
  allCampsites, 
  mockReviews,
  getCampsiteById 
} from '@/data';

const Profile = () => {
  // Mock current user - in a real app this would come from auth context
  const currentUser = mockUsers[0]; // <PERSON>
  const [isEditing, setIsEditing] = useState(false);
  const [userProfile, setUserProfile] = useState(currentUser);

  // Get user's favorite campsites
  const favoriteCampsites = currentUser.favorites.map(id => getCampsiteById(id)).filter(Boolean);
  
  // Get user's reviews
  const userReviews = mockReviews.filter(review => review.userId === currentUser.id);

  // Mock booking history
  const bookingHistory = [
    {
      id: 'booking-1',
      campsiteId: '1',
      campsite: getCampsiteById('1'),
      checkIn: '2024-07-15',
      checkOut: '2024-07-18',
      guests: 2,
      total: 2550,
      status: 'completed'
    },
    {
      id: 'booking-2',
      campsiteId: '3',
      campsite: getCampsiteById('3'),
      checkIn: '2024-08-20',
      checkOut: '2024-08-23',
      guests: 4,
      total: 2850,
      status: 'completed'
    },
    {
      id: 'booking-3',
      campsiteId: '2',
      campsite: getCampsiteById('2'),
      checkIn: '2024-10-15',
      checkOut: '2024-10-18',
      guests: 2,
      total: 3600,
      status: 'upcoming'
    }
  ];

  const handleSaveProfile = () => {
    // In a real app, this would save to an API
    console.log('Saving profile:', userProfile);
    setIsEditing(false);
  };

  const removeFavorite = (campsiteId: string) => {
    // In a real app, this would update the user's favorites via API
    console.log('Removing favorite:', campsiteId);
  };

  return (
    <div className="min-h-screen bg-background">
      <Navigation />
      
      <div className="container mx-auto px-4 py-8">
        {/* Profile Header */}
        <div className="flex items-center gap-6 mb-8">
          <Avatar className="h-24 w-24">
            <AvatarImage src={currentUser.avatar} />
            <AvatarFallback className="text-2xl">{currentUser.name.charAt(0)}</AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <div className="flex items-center gap-3 mb-2">
              <h1 className="text-3xl font-bold">{currentUser.name}</h1>
              {currentUser.verified && (
                <Badge variant="secondary">
                  <Shield className="h-3 w-3 mr-1" />
                  Verified
                </Badge>
              )}
              {currentUser.subscriptionTier === 'premium' && (
                <Badge className="bg-gradient-to-r from-purple-500 to-pink-500">
                  <Award className="h-3 w-3 mr-1" />
                  Premium
                </Badge>
              )}
            </div>
            
            <p className="text-muted-foreground mb-3">
              Member since {new Date(currentUser.joinDate).getFullYear()} • {currentUser.reviewCount} reviews
            </p>
            
            <div className="flex flex-wrap gap-2">
              {currentUser.badges.map(badge => (
                <Badge key={badge} variant="outline" className="text-xs">
                  {badge}
                </Badge>
              ))}
            </div>
          </div>
          
          <Button variant="outline" onClick={() => setIsEditing(!isEditing)}>
            <Edit className="h-4 w-4 mr-2" />
            Edit Profile
          </Button>
        </div>

        {/* Profile Tabs */}
        <Tabs defaultValue="favorites" className="space-y-6">
          <TabsList className="grid w-full grid-cols-5">
            <TabsTrigger value="favorites">Favorites</TabsTrigger>
            <TabsTrigger value="bookings">Bookings</TabsTrigger>
            <TabsTrigger value="reviews">Reviews</TabsTrigger>
            <TabsTrigger value="settings">Settings</TabsTrigger>
            <TabsTrigger value="account">Account</TabsTrigger>
          </TabsList>

          {/* Favorites Tab */}
          <TabsContent value="favorites" className="space-y-6">
            <div className="flex items-center justify-between">
              <h2 className="text-2xl font-semibold">Your Favorites</h2>
              <p className="text-muted-foreground">{favoriteCampsites.length} saved campsites</p>
            </div>
            
            {favoriteCampsites.length === 0 ? (
              <Card>
                <CardContent className="flex flex-col items-center justify-center py-12">
                  <Heart className="h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No favorites yet</h3>
                  <p className="text-muted-foreground text-center mb-4">
                    Start exploring and save campsites you'd like to visit
                  </p>
                  <Button asChild>
                    <a href="/search">Browse Campsites</a>
                  </Button>
                </CardContent>
              </Card>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {favoriteCampsites.map(campsite => campsite && (
                  <Card key={campsite.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    <div className="relative h-48">
                      <img 
                        src={campsite.image} 
                        alt={campsite.name}
                        className="w-full h-full object-cover"
                      />
                      <Button
                        variant="secondary"
                        size="sm"
                        className="absolute top-2 right-2"
                        onClick={() => removeFavorite(campsite.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="font-semibold text-lg">{campsite.name}</h3>
                        <div className="flex items-center">
                          <Star className="h-4 w-4 fill-yellow-400 text-yellow-400 mr-1" />
                          <span className="text-sm font-medium">{campsite.rating}</span>
                        </div>
                      </div>
                      
                      <div className="flex items-center text-muted-foreground mb-3">
                        <MapPin className="h-4 w-4 mr-1" />
                        <span className="text-sm">{campsite.location}, {campsite.province}</span>
                      </div>
                      
                      <div className="flex items-center justify-between">
                        <span className="font-semibold">R{campsite.price}/night</span>
                        <Button size="sm" asChild>
                          <a href={`/campsite/${campsite.id}`}>View Details</a>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            )}
          </TabsContent>

          {/* Bookings Tab */}
          <TabsContent value="bookings" className="space-y-6">
            <h2 className="text-2xl font-semibold">Your Bookings</h2>
            
            <div className="space-y-4">
              {bookingHistory.map(booking => booking.campsite && (
                <Card key={booking.id}>
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div className="w-20 h-20 rounded-lg overflow-hidden flex-shrink-0">
                        <img 
                          src={booking.campsite.image} 
                          alt={booking.campsite.name}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-lg">{booking.campsite.name}</h3>
                          <Badge variant={booking.status === 'completed' ? 'secondary' : 'default'}>
                            {booking.status}
                          </Badge>
                        </div>
                        
                        <div className="flex items-center text-muted-foreground mb-2">
                          <MapPin className="h-4 w-4 mr-1" />
                          <span className="text-sm">{booking.campsite.location}, {booking.campsite.province}</span>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            {new Date(booking.checkIn).toLocaleDateString()} - {new Date(booking.checkOut).toLocaleDateString()}
                          </div>
                          <span>{booking.guests} guests</span>
                          <span className="font-semibold text-foreground">R{booking.total}</span>
                        </div>
                      </div>
                      
                      <div className="flex flex-col gap-2">
                        <Button variant="outline" size="sm" asChild>
                          <a href={`/campsite/${booking.campsiteId}`}>View Campsite</a>
                        </Button>
                        {booking.status === 'completed' && (
                          <Button variant="outline" size="sm">
                            Write Review
                          </Button>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Reviews Tab */}
          <TabsContent value="reviews" className="space-y-6">
            <h2 className="text-2xl font-semibold">Your Reviews</h2>
            
            <div className="space-y-4">
              {userReviews.map(review => {
                const campsite = getCampsiteById(review.campsiteId);
                return campsite && (
                  <Card key={review.id}>
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                          <img 
                            src={campsite.image} 
                            alt={campsite.name}
                            className="w-full h-full object-cover"
                          />
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <h3 className="font-semibold">{campsite.name}</h3>
                            <div className="flex items-center">
                              {[1, 2, 3, 4, 5].map((star) => (
                                <Star
                                  key={star}
                                  className={`h-4 w-4 ${
                                    star <= review.rating 
                                      ? 'fill-yellow-400 text-yellow-400' 
                                      : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                          
                          <h4 className="font-medium mb-2">{review.title}</h4>
                          <p className="text-muted-foreground text-sm mb-2">{review.comment}</p>
                          
                          <div className="flex items-center justify-between text-sm text-muted-foreground">
                            <span>{new Date(review.date).toLocaleDateString()}</span>
                            <span>{review.helpful} people found this helpful</span>
                          </div>
                        </div>
                        
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </TabsContent>

          {/* Settings Tab */}
          <TabsContent value="settings" className="space-y-6">
            <h2 className="text-2xl font-semibold">Notification Settings</h2>
            
            <Card>
              <CardContent className="p-6 space-y-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Email Notifications</h3>
                    <p className="text-sm text-muted-foreground">Receive updates about your bookings and account</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Marketing Emails</h3>
                    <p className="text-sm text-muted-foreground">Get notified about new campsites and special offers</p>
                  </div>
                  <Switch />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">SMS Notifications</h3>
                    <p className="text-sm text-muted-foreground">Receive booking confirmations and reminders via SMS</p>
                  </div>
                  <Switch defaultChecked />
                </div>
                
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-medium">Push Notifications</h3>
                    <p className="text-sm text-muted-foreground">Get notified about messages and booking updates</p>
                  </div>
                  <Switch defaultChecked />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Account Tab */}
          <TabsContent value="account" className="space-y-6">
            <h2 className="text-2xl font-semibold">Account Settings</h2>
            
            {isEditing ? (
              <Card>
                <CardContent className="p-6 space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="firstName">First Name</Label>
                      <Input 
                        id="firstName" 
                        value={userProfile.name.split(' ')[0]}
                        onChange={(e) => setUserProfile(prev => ({ 
                          ...prev, 
                          name: e.target.value + ' ' + prev.name.split(' ')[1] 
                        }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="lastName">Last Name</Label>
                      <Input 
                        id="lastName" 
                        value={userProfile.name.split(' ')[1]}
                        onChange={(e) => setUserProfile(prev => ({ 
                          ...prev, 
                          name: prev.name.split(' ')[0] + ' ' + e.target.value 
                        }))}
                      />
                    </div>
                  </div>
                  
                  <div>
                    <Label htmlFor="email">Email</Label>
                    <Input 
                      id="email" 
                      type="email" 
                      value={userProfile.email}
                      onChange={(e) => setUserProfile(prev => ({ ...prev, email: e.target.value }))}
                    />
                  </div>
                  
                  <div className="flex gap-2">
                    <Button onClick={handleSaveProfile}>Save Changes</Button>
                    <Button variant="outline" onClick={() => setIsEditing(false)}>Cancel</Button>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 space-y-4">
                  <div>
                    <Label>Name</Label>
                    <p className="text-lg">{currentUser.name}</p>
                  </div>
                  
                  <div>
                    <Label>Email</Label>
                    <p className="text-lg">{currentUser.email}</p>
                  </div>
                  
                  <div>
                    <Label>Member Since</Label>
                    <p className="text-lg">{new Date(currentUser.joinDate).toLocaleDateString()}</p>
                  </div>
                  
                  <div>
                    <Label>Subscription</Label>
                    <div className="flex items-center gap-2">
                      <p className="text-lg capitalize">{currentUser.subscriptionTier}</p>
                      {currentUser.subscriptionTier === 'free' && (
                        <Button size="sm" variant="outline">
                          Upgrade to Premium
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Profile;
